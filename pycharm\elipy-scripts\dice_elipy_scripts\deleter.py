"""
deleter.py
"""

import re
import os
from collections import ChainMap
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.delete_utils import delete_empty_folders
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
import glob
import json
from datetime import datetime
import multiprocessing
from multiprocessing.pool import ThreadPool
import six
import click
from typing import Any, Dict, Iterable, Optional

from azure.core.exceptions import ServiceRequestError as AzureServiceRequestError

from elipy2 import (
    expire,
    LOGGER,
    SETTINGS,
    avalanche,
    core,
    symbols,
    build_metadata_utils,
    az_utils,
)
from elipy2.azcopy_client import AZCopyClient
from elipy2.exceptions import ELIPYException, ConfigValueNotFoundException
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("deleter")
@click.option(
    "--empty-folders",
    is_flag=True,
    help="Crawl project network path for empty folders and delete them.",
    default=False,
    required=False,
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Print the delete list without modifying it.",
    required=False,
)
@click.option(
    "--no-shift-delete",
    is_flag=True,
    help="Do not delete shift entries.",
    required=False,
)
@click.option("--avalanche-host", help="Host e.g. cg-buildrepo.eu.ad.ea.com", required=False)
@click.option("--clean-record", is_flag=True, help="Clean db records.", required=False)
@click.option(
    "--use-onefs-api",
    help="Delete using onefs_api method",
    is_flag=True,
    required=False,
)
@click.option(
    "--include-path-retention",
    help="Perform cleanup on the paths in the path_retention settings",
    is_flag=True,
    required=False,
)
@click.option(
    "--include-azure-path-retention",
    help="Perform cleanup on the paths in the azure_path_retention settings",
    is_flag=True,
    required=False,
)
@click.option(
    "--exclude-retention-categories",
    help="Do not perform cleanup on the retention_categories settings",
    is_flag=True,
    required=False,
)
@click.option(
    "--include",
    help="Include only these categories. By default all categories are included",
    multiple=True,
)
@click.option(
    "--exclude",
    help="Exclude these categories. Excludes take precedence over includes",
    multiple=True,
)
@click.option(
    "--clean-symstore-days",
    default=None,
    help="Clean up symstores files older than days",
)
@click.option(
    "--creation-time-deletion",
    default=False,
    is_flag=True,
    required=False,
    help="Clean up build dirs based on creation time",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(  # pylint: disable=too-many-positional-arguments
    _,
    empty_folders,
    dry_run,
    no_shift_delete,
    avalanche_host,
    clean_record=False,
    use_onefs_api=False,
    include_path_retention=False,
    include_azure_path_retention=False,
    exclude_retention_categories=False,
    include=None,
    exclude=None,
    clean_symstore_days=None,
    creation_time_deletion=False,
):
    """
    foreach $path: retains $maxamount of items. retention map is expected in SETTINGS

    If invoked with `--dry-run`, print what would get deleted without modifying it
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info("performaing a --dry-run? {0}".format(dry_run))

    utils = expire.ExpireUtils()
    use_onefs_api = SETTINGS.get("use_onefs_api") == "true"
    exceptionlist = []
    exceptionlist += cleanup_shift(utils, no_shift_delete, dry_run, use_onefs_api)
    exceptionlist += cleanup_retention_paths(utils, include_path_retention, dry_run, use_onefs_api)
    if not exclude_retention_categories:
        exceptionlist += cleanup_builds(
            dry_run, use_onefs_api, include, exclude, creation_time_deletion
        )
    exceptionlist += cleanup_azure_retention_paths(include_azure_path_retention, dry_run)
    exceptionlist += cleanup_avalanche_records(avalanche_host, dry_run, clean_record)
    exceptionlist += cleanup_symstores(dry_run, clean_symstore_days)

    if empty_folders:
        exceptionlist += cleanup_empty_folders(dry_run)

    if exceptionlist:
        for i in exceptionlist:
            LOGGER.error(i, exc_info=True)
        raise ELIPYException("Failed to delete all files.")


def cleanup_empty_folders(dry_run):
    # type: (bool) -> List[Exception]
    """
    Crawls each retention category under build path and tries to remove empty-like folders.
    """
    exceptionlist = []

    categories = SETTINGS.get("retention_categories")
    for category in categories:
        category_path = os.path.join(SETTINGS.get("build_share"), category)
        LOGGER.debug(category_path)
        exceptionlist.extend(
            delete_empty_folders(
                dry_run=dry_run, files_equals_empty=["build.json"], path=category_path
            )
        )

    return exceptionlist


def cleanup_shift(utils, no_shift_delete, dry_run, use_onefs_api):
    "Cleanup shift builds"
    exceptionlist = []
    try:
        if not no_shift_delete:
            utils.keep_n_at_path(
                SETTINGS.get("shift_submission_path"),
                SETTINGS.get("shift_retention"),
                dry_run,
                use_onefs_api,
            )
    except Exception as exc:
        LOGGER.error(exc, exc_info=True)
        exceptionlist.append(exc)

    return exceptionlist


def cleanup_retention_paths(utils, include_path_retention, dry_run, use_onefs_api):
    "Cleanup retention paths"
    exceptionlist = []
    try:
        if include_path_retention:
            for each in SETTINGS.get("path_retention"):
                for path, maxamount in each.items():
                    # Ensure consistent path format in log messages
                    normalized_path = path.replace("/", "\\")
                    if os.path.isdir(path):
                        LOGGER.info("retaining {0} at file:{1}".format(maxamount, normalized_path))
                        utils.keep_n_at_path(path, maxamount, dry_run, use_onefs_api)
                    else:
                        LOGGER.warning("Directory does not exist, skipping: {0}".format(path))
        else:
            LOGGER.info("Skipping path_retention files")
    except ConfigValueNotFoundException:
        pass
    except Exception as exc:
        LOGGER.error(exc, exc_info=True)
        exceptionlist.append(exc)

    return exceptionlist


def cleanup_azure_retention_paths(include_azure_path_retention, dry_run):
    """
    Cleanup Azure Fileshare retention paths
    @param include_azure_path_retention: skip if False
    @param dry_run: don't actually delete anything if True
    """
    exceptionlist = []
    # pylint: disable=too-many-nested-blocks
    try:
        if include_azure_path_retention:
            for az_storage_account in SETTINGS.get("azure_path_retention"):
                secret_context = az_storage_account.get("secret_context")
                for fileshare in az_storage_account.get("fileshares", []):
                    fileshare_name = fileshare.get("fileshare_name")
                    for path_item in fileshare.get("paths", []):
                        for fileshare_path, maxamount in path_item.items():
                            LOGGER.info(
                                "Deleting all but newest {} dirs at fileshare '{}' path {}".format(
                                    maxamount, fileshare_name, fileshare_path
                                )
                            )
                            LOGGER.info("maxamount is {}".format(maxamount))
                            keep_n_at_azure_path(
                                fileshare_path,
                                maxamount,
                                dry_run,
                                secret_context,
                                fileshare_name,
                            )
        else:
            LOGGER.info("Skipping azure_fileshare_path_retention files")
    except ConfigValueNotFoundException:
        pass
    except Exception as exc:
        LOGGER.error(exc, exc_info=True)
        exceptionlist.append(exc)
    # pylint: enable=too-many-nested-blocks
    return exceptionlist


def keep_n_at_azure_path(  # pylint: disable=too-many-positional-arguments
    fileshare_path,
    maxamount,
    dry_run,
    secret_context,
    fileshare_name,
    azure_domain="file.core.windows.net",
):
    """
    Delete all but the most recent n directories at the given path
    @param fileshare_path: path to the fileshare directory (in the form "Code/trunk-code-dev")
    @param maxamount: number of directories to keep
    @param dry_run: if True, print what would get deleted without modifying it
    @param secret_context: which secret in the elipy config to use.
    @param fileshare_name: name of the fileshare within the storage account
    """
    try:
        dirs_at_path_generator = az_utils.yield_azfileshare_path_contents_metadata(
            fileshare_path, secret_context=secret_context, share_name=fileshare_name
        )
        try:
            dirs_at_path = list(dirs_at_path_generator)

        except AzureServiceRequestError as exc:
            LOGGER.error(exc, exc_info=True)

            raise ELIPYException(
                "Failed to complete Azure request. Check that DNS resolution "
                "between the agent and the Azure resource is working or that the "
                "agent has an appropriate configuration in the hosts file "
                "(https://go.ea.com/slack_ref)"
            )

        # The name of the dir should be the CL number so sort only by digits in the name
        dirs_at_path.sort(
            key=lambda x: int(re.search(r"\d+", x["name"]).group())
        )  # lowest CL first

        azc_client = AZCopyClient(
            secret_context, share_name=fileshare_name, client_permissions="rdl"
        )

        directory_paths_to_delete = []
        for directory in dirs_at_path[: -int(maxamount)]:
            destination = "/".join(
                [
                    part.strip("/")
                    for part in [
                        f"https://{azc_client.account_name}.{azure_domain}",
                        fileshare_name,
                        fileshare_path,
                        directory["name"],
                    ]
                ]
            )

            directory_paths_to_delete.append(destination)

        LOGGER.info("directory_paths_to_delete: {}".format(directory_paths_to_delete))

        for destination in directory_paths_to_delete:
            if dry_run:
                LOGGER.info("Would delete folder {}".format(destination))
            else:
                LOGGER.info("Deleting folder {}".format(destination))
                azc_client.remove_dir(destination)

    except Exception as exc:
        LOGGER.error(exc, exc_info=True)
        raise ELIPYException(
            f"Failed to keep_n_at_azure_path {fileshare_path} with secret_context {secret_context}."
        )


def filter_categories(
    categories: Dict[str, Any],
    include: Optional[Iterable] = None,
    exclude: Optional[Iterable] = None,
):
    "Get a list of categories"
    include = include if include else []
    exclude = exclude if exclude else []

    selected_categories = {}

    invalid_includes = [k for k in include if k not in categories.keys()]
    invalid_excludes = [k for k in exclude if k not in categories.keys()]
    if invalid_includes or invalid_excludes:
        raise ValueError(
            "Invalid values passed for include ({0}) or exclude ({1})".format(
                invalid_includes, invalid_excludes
            )
        )

    included = {k: v for k, v in categories.items() if k in include}
    excluded = {k: v for k, v in categories.items() if k in exclude}
    if not included:
        included = categories

    selected_categories = {
        k: v for k, v in categories.items() if (k in included and k not in excluded)
    }

    return selected_categories


def _log_branch_config_if_ch1_bflabs_release(category_name, branch_name, retention_value):
    """Log branch config if it's a ch1-bflabs-release branch."""
    if "ch1-bflabs-release" in branch_name.lower():
        LOGGER.warning(
            "Found ch1-bflabs-release retention config: category=%s, branch=%s, value=%s",
            category_name,
            branch_name,
            retention_value,
        )


def _log_ch1_bflabs_release_config(selected_categories):
    """Log configuration for ch1-bflabs-release branches."""
    for category_name, category_data in selected_categories.items():
        if "code" in category_name.lower():
            for branch_config in category_data:
                if isinstance(branch_config, dict):
                    for branch_name, retention_value in branch_config.items():
                        _log_branch_config_if_ch1_bflabs_release(
                            category_name, branch_name, retention_value
                        )


def cleanup_builds(
    dry_run, use_onefs_api, include=None, exclude=None, creation_time_deletion=False
):
    "cleanup builds"
    exceptionlist = []

    categories = SETTINGS.get("retention_categories")
    release_candidates_to_keep_count = SETTINGS.get("release_candidate_retention", default=56)

    # Debug logging to trace retention configuration
    LOGGER.debug("Raw retention_categories from SETTINGS: %s", categories)

    selected_categories = filter_categories(categories, include, exclude)

    LOGGER.info("Selected categories are: %s", selected_categories)

    # Additional debug logging for ch1-bflabs-release specifically
    _log_ch1_bflabs_release_config(selected_categories)

    if creation_time_deletion:
        exc = delete_build_with_creation_time(dry_run, selected_categories)
        if exc:
            exceptionlist.append(exc)
    else:
        for sub_path, branch_list in selected_categories.items():
            branch_settings = dict(ChainMap(*branch_list))
            # Create case-insensitive branch settings to handle case mismatches
            # where config has 'CH1-event' but filesystem discovery returns 'ch1-event'
            branch_settings_normalized = {k.lower(): v for k, v in branch_settings.items()}
            path = os.path.join(SETTINGS.get("build_share"), sub_path)
            branch_set = get_branch_set_under_path(path)
            clean_up_args = []

            for branch in branch_set:
                maxamount = branch_settings_normalized.get(
                    branch.lower(), branch_settings["default"]
                )

                # Debug logging for ch1-bflabs-release specifically
                if "ch1-bflabs-release" in branch.lower():
                    LOGGER.warning(
                        "Processing ch1-bflabs-release: sub_path=%s, branch=%s, maxamount=%s, "
                        "branch_settings=%s, branch_settings_normalized=%s",
                        sub_path,
                        branch,
                        maxamount,
                        branch_settings,
                        branch_settings_normalized,
                    )

                # Debug logging for sanitizers paths
                if "sanitizers" in branch.lower():
                    LOGGER.warning(
                        "Processing sanitizers branch: sub_path=%s, branch=%s, maxamount=%s, "
                        "branch_settings=%s, branch_settings_normalized=%s",
                        sub_path,
                        branch,
                        maxamount,
                        branch_settings,
                        branch_settings_normalized,
                    )

                branch_path = os.path.join(path, branch)
                # Ensure consistent path format in log messages
                normalized_branch_path = branch_path.replace("/", "\\")
                LOGGER.info("retaining {0} at file:{1}".format(maxamount, normalized_branch_path))

                clean_up_args.append(
                    (
                        branch_path,
                        maxamount,
                        dry_run,
                        use_onefs_api,
                        release_candidates_to_keep_count,
                    )
                )
            if clean_up_args != []:
                try:
                    pool = ThreadPool(processes=multiprocessing.cpu_count() - 1)
                    pool.map(unpack_expire, clean_up_args)
                    pool.close()
                    pool.join()
                except Exception as exc:
                    LOGGER.error(exc, exc_info=True)
                    exceptionlist.append(exc)
    return exceptionlist


def cleanup_avalanche_records(avalanche_host, dry_run, clean_record):
    "Cleanup avalanche records"
    exceptionlist = []
    if clean_record:
        try:
            check_and_drop_records(avalanche_host, dry_run)
        except Exception as exc:
            LOGGER.error(exc, exc_info=True)
            exceptionlist.append(exc)
    else:
        LOGGER.warning("Skipping Avalanche db deletion.")
    return exceptionlist


def cleanup_symstores(dry_run, cleanup_symstores_days):
    "Cleanup avalanche records"
    exceptionlist = []
    if cleanup_symstores_days:
        try:
            for platform in ["ps", "ms"]:
                symstore_path = symbols.SymbolsUtils().symbol_store_path.format(platform)
                core.run_age_store(symstore_path, cleanup_symstores_days, dry_run)
        except Exception as exc:
            LOGGER.error(exc, exc_info=True)
            exceptionlist.append(exc)
    else:
        LOGGER.warning("Skipping symstore cleanup.")
    return exceptionlist


def check_and_drop_records(avalanche_host, dry_run, percent_required_free=30):
    """
    Checks space left on central Avalanche store and delete oldest record and mark it in bilbo.
    """
    # Creating http + urls
    avalanche_url = "http://" + avalanche_host
    db_all_url = six.moves.urllib.parse.urljoin(avalanche_url, "/db/all")

    # Ping Avalanche for json/all
    try:
        with six.moves.urllib.request.urlopen(db_all_url) as response:
            data_read = response.read().decode("utf-8")
    except Exception as exc:
        LOGGER.warning("No Response from server {}".format(exc))
    else:
        data = json.loads(data_read)

        # Sort dates in reverse old so oldest is first in list.
        sorted_date = sorted(
            data, key=lambda x: datetime.strptime(x["updated"], "%Y-%m-%dT%H:%M:%SZ")
        )

        # Getting primary info from Avalanche.
        primary_info_url = six.moves.urllib.parse.urljoin(
            avalanche_url, "storage/pools/primary.json"
        )
        with six.moves.urllib.request.urlopen(primary_info_url) as response:
            data_read = response.read()
        data = json.loads(data_read)
        diskpercent = (
            float(data["Primary"]["pools"][0]["freeDiskSpace"])
            / float(data["Primary"]["pools"][0]["totalDiskSpace"])
            * 100
        )
        percent_left = avalanche.get_avalanche_free_space(0, avalanche_url)

        if (percent_left < percent_required_free) or (diskpercent < percent_required_free):
            for item in sorted_date:
                LOGGER.info(item["id"] + " : " + item["updated"])

            set_range = 1
            if not dry_run:
                if diskpercent < percent_required_free:
                    set_range = 9

                # Displaying Build to be Deleted
                LOGGER.warning("Less than {0} percent free HDD space left".format(diskpercent))

                if sorted_date:
                    LOGGER.info("The following record(s) will be deleted:")
                    for i in range(set_range):
                        record_to_delete = sorted_date[i]
                        LOGGER.info(record_to_delete["id"] + " : " + record_to_delete["updated"])
                        avalanche.drop_build_record(record_to_delete["id"], avalanche_url)
                        reverse_bilbo_delete(dry_run, record_to_delete, avalanche_host)
        else:
            LOGGER.info("Space avaliable more than minimum setting: {}".format(percent_left))
            LOGGER.info("Skipping deletion.")


def reverse_bilbo_delete(dry_run, record_to_delete, avalanche_host):
    """
    Removes bilbo record by doing a reverse platform lookup.
    """
    metadata_manager = build_metadata_utils.setup_metadata_manager()

    split_list = record_to_delete["id"].split(".")
    for platform in avalanche.get_reverse_avalanche_platform(split_list[2]):
        # Bilbo Path = Clone_Host, Destination_DB, Platform,
        # Data_branch, Data_CL, Code_Brance, Code_CL.
        bilbo_path = (
            avalanche_host
            + "."
            + record_to_delete["id"]
            + "."
            + platform
            + "."
            + split_list[1]
            + "."
            + split_list[3]
            + "."
            + split_list[1]
            + "."
            + split_list[4]
        )

        LOGGER.info("Getting Bilbo Path: {}".format(bilbo_path))
        if not dry_run:
            metadata_manager.delete_build(bilbo_path)


def get_branch_set_under_path(path):
    """
    Discovers branch names under the given path using disk-based scanning
    """
    branch_set = set()

    # Disk-based branch discovery
    if os.path.exists(path):
        disk_branches = _get_disk_branches_for_path(path)
        branch_set.update(disk_branches)
        LOGGER.info(
            "Disk-based discovery found %d branches: %s", len(disk_branches), sorted(disk_branches)
        )
    else:
        LOGGER.warning("Path does not exist, skipping branch discovery: %s", path)

    return branch_set


def _get_disk_branches_for_path(path):
    """
    Get branch names from disk based on known path patterns:
    - path/code/branch/CL
    - path/frosty/BattlefieldGame/branch/CL1/branch/CL2
    - path/webexport/version/branch/CL1_CL2
    """
    branches = set()

    try:
        path_lower = path.lower()

        if "frosty" in path_lower:
            branches.update(_scan_frosty_branches(path))
        elif "code" in path_lower:
            branches.update(_scan_code_branches(path))
        elif "webexport" in path_lower:
            branches.update(_scan_webexport_branches(path))
        else:
            branches.update(_scan_default_branches(path))

    except (OSError, PermissionError) as exc:
        LOGGER.warning("Error scanning disk for branches under %s: %s", path, exc)

    return branches


def _scan_frosty_branches(path):
    """Scan frosty paths for branches: frosty/BattlefieldGame/branch/CL1/branch/CL2"""
    branches = set()
    for item in os.listdir(path):
        item_path = os.path.join(path, item)
        if os.path.isdir(item_path):
            if _is_frosty_branch_directory(item_path):
                branches.add(item)
    return branches


def _scan_code_branches(path):
    """Scan code paths for branches: code/branch/CL

    Special handling for sanitizers paths where structure is:
    code/ch1-content-dev-sanitizers/asan/CL
    code/ch1-content-dev-sanitizers/ubsan/CL
    """
    branches = set()

    for item in os.listdir(path):
        item_path = os.path.join(path, item)
        if os.path.isdir(item_path):
            # Check if this is a sanitizers directory
            if "sanitizers" in item.lower():
                # For sanitizers, look for subdirectories (asan, ubsan) and treat them as separate branches
                try:
                    subitems = os.listdir(item_path)
                    for subitem in subitems:
                        subitem_path = os.path.join(item_path, subitem)
                        if os.path.isdir(subitem_path):
                            # Check if this subdirectory contains CL directories
                            try:
                                subsubitems = os.listdir(subitem_path)
                                has_cl_dirs = any(
                                    subsub.isdigit() and len(subsub) >= 7
                                    for subsub in subsubitems
                                    if os.path.isdir(os.path.join(subitem_path, subsub))
                                )
                                if has_cl_dirs:
                                    # Add the full path as branch name for sanitizers
                                    # This will create branch names like "ch1-content-dev-sanitizers/asan"
                                    branches.add(f"{item}/{subitem}")
                            except (OSError, PermissionError):
                                # If we can't read the directory, still consider it a potential branch
                                branches.add(f"{item}/{subitem}")
                except (OSError, PermissionError):
                    # If we can't read the sanitizers directory, treat it as a regular branch
                    branches.add(item)
            else:
                # Standard branch handling
                branches.add(item)

    return branches


def _scan_webexport_branches(path):
    """Scan webexport paths for branches: webexport/version/branch/CL1_CL2"""
    branches = set()
    for version_dir in os.listdir(path):
        version_path = os.path.join(path, version_dir)
        if os.path.isdir(version_path):
            try:
                for branch_dir in os.listdir(version_path):
                    branch_path = os.path.join(version_path, branch_dir)
                    if os.path.isdir(branch_path):
                        branches.add(branch_dir)
            except (OSError, PermissionError):
                continue
    return branches


def _scan_default_branches(path):
    """Scan unknown path types: assume direct subdirectories are branches"""
    branches = set()
    for item in os.listdir(path):
        item_path = os.path.join(path, item)
        if os.path.isdir(item_path):
            branches.add(item)
    return branches


def _is_frosty_branch_directory(item_path):
    """Check if directory contains CL subdirectories indicating it's a branch"""
    try:
        subitems = os.listdir(item_path)
        return any(
            subitem.isdigit() and len(subitem) >= 7
            for subitem in subitems
            if os.path.isdir(os.path.join(item_path, subitem))
        )
    except (OSError, PermissionError):
        # If we can't read the directory, still consider it a potential branch
        return True


def delete_build_with_creation_time(dry_run, selected_categories):
    """
    Cleanup builds based on creation time
    """
    metadata_manager = build_metadata_utils.setup_metadata_manager()
    exceptionlist = []
    for sub_path, branch_list in selected_categories.items():
        branch_settings = {}
        if "default" not in branch_list:
            branch_settings = dict(ChainMap(*branch_list))
        path = os.path.join(SETTINGS.get("build_share"), sub_path)
        for branch in branch_settings:
            maxamount = branch_settings.get(branch, branch_settings["default"])
            delete_path = os.path.join(path, branch)
            # Ensure consistent path format in log messages
            normalized_delete_path = delete_path.replace("/", "\\")
            LOGGER.info("retaining {0} at file:{1}".format(maxamount, normalized_delete_path))
            try:
                dirs = list(filter(os.path.isdir, glob.glob(delete_path + "\\*")))
                dirs.sort(key=os.path.getctime, reverse=True)
                deleting_dirs = dirs[maxamount:]
                LOGGER.info("Builds will be deleted {0}".format(deleting_dirs))
                if not dry_run and deleting_dirs:
                    for build in deleting_dirs:
                        core.delete_filer_folder(build, 1)
                        LOGGER.info("marking as deleted in bilbo:   %s", build)
                        metadata_manager.delete_build(build)
            except Exception as exc:
                LOGGER.error(exc, exc_info=True)
                exceptionlist.append(exc)
    return exceptionlist


def unpack_expire(args):
    """
    Helper function to unpack arguments from pool map function
    """
    utils = expire.ExpireUtils()
    path, maxamount, dry_run, use_onefs_api, release_candidates_to_keep_count = args
    utils.expire(
        path,
        maxamount,
        dry_run,
        use_onefs_api,
        release_candidates_to_keep_count=release_candidates_to_keep_count,
    )
