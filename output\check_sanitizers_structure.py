#!/usr/bin/env python3
"""
Check the actual structure of the sanitizers directory
"""
import os

def check_directory_structure():
    """Check the actual directory structure"""
    base_path = r"\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers"
    
    print(f"Checking structure at: {base_path}")
    print("=" * 80)
    
    try:
        items = os.listdir(base_path)
        print(f"Found {len(items)} items in parent directory:")
        
        cl_dirs_in_parent = 0
        non_cl_dirs = []
        
        for item in sorted(items):
            item_path = os.path.join(base_path, item)
            if os.path.isdir(item_path):
                if item.isdigit() and len(item) >= 7:
                    cl_dirs_in_parent += 1
                    if cl_dirs_in_parent <= 5:  # Show first 5
                        print(f"  📁 {item}/ (CL directory)")
                else:
                    non_cl_dirs.append(item)
                    print(f"  📁 {item}/ (non-CL directory)")
            else:
                print(f"  📄 {item}")
        
        if cl_dirs_in_parent > 5:
            print(f"  ... and {cl_dirs_in_parent - 5} more CL directories")
            
        print(f"\nSummary:")
        print(f"  - CL directories in parent: {cl_dirs_in_parent}")
        print(f"  - Non-CL directories: {non_cl_dirs}")
        
        # Check subdirectories
        for subdir in non_cl_dirs:
            subdir_path = os.path.join(base_path, subdir)
            try:
                subitems = os.listdir(subdir_path)
                cl_count = sum(1 for subitem in subitems 
                             if subitem.isdigit() and len(subitem) >= 7 
                             and os.path.isdir(os.path.join(subdir_path, subitem)))
                print(f"  - {subdir}/: {cl_count} CL directories")
            except (OSError, PermissionError) as e:
                print(f"  - {subdir}/: access denied ({e})")
                
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    check_directory_structure()
