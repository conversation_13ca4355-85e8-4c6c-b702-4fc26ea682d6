﻿************* Module dice_elipy_scripts.deleter
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:109:0: R0913: Too many arguments (14/5) (too-many-arguments)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:133:16: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:133:16: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:188:11: W0718: Catching too general exception Exception (broad-exception-caught)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:213:11: W0718: Catching too general exception Exception (broad-exception-caught)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:205:36: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:205:36: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:208:39: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:208:39: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:253:11: W0718: Catching too general exception Exception (broad-exception-caught)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:237:32: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:237:32: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:241:40: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:241:40: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:260:0: R0913: Too many arguments (6/5) (too-many-arguments)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:286:12: W0707: Consider explicitly re-raising using 'raise ELIPYException('Failed to complete Azure request. Check that DNS resolution between the agent and the Azure resource is working or that the agent has an appropriate configuration in the hosts file (https://go.ea.com/slack_ref)') from exc' (raise-missing-from)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:318:20: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:318:20: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:322:28: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:322:28: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:324:28: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:324:28: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:329:8: W0707: Consider explicitly re-raising using 'raise ELIPYException(f'Failed to keep_n_at_azure_path {fileshare_path} with secret_context {secret_context}.') from exc' (raise-missing-from)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:349:12: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:389:0: R0914: Too many local variables (22/15) (too-many-locals)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:454:28: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:454:28: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:465:15: C1803: "clean_up_args != []" can be simplified to "clean_up_args", if it is strictly a sequence, as an empty list is falsey (use-implicit-booleaness-not-comparison)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:471:23: W0718: Catching too general exception Exception (broad-exception-caught)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:483:15: W0718: Catching too general exception Exception (broad-exception-caught)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:499:15: W0718: Catching too general exception Exception (broad-exception-caught)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:507:0: R0914: Too many local variables (17/15) (too-many-locals)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:519:11: W0718: Catching too general exception Exception (broad-exception-caught)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:520:23: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:520:23: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:553:31: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:553:31: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:563:24: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:563:24: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:593:20: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:593:20: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:686:33: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:696:33: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:744:0: R0914: Too many local variables (16/15) (too-many-locals)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:760:24: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:760:24: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:771:19: W0718: Catching too general exception Exception (broad-exception-caught)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:765:28: W1202: Use lazy % formatting in logging functions (logging-format-interpolation)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:765:28: C0209: Formatting a regular string which could be an f-string (consider-using-f-string)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:11:0: C0411: standard import "glob" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:12:0: C0411: standard import "json" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:13:0: C0411: standard import "datetime.datetime" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:14:0: C0411: standard import "multiprocessing" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:15:0: C0411: standard import "multiprocessing.pool.ThreadPool" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:16:0: C0411: third party import "six" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:17:0: C0411: third party import "click" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:18:0: C0411: standard import "typing.Any" should be placed before third party imports "six", "click" and first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:20:0: C0411: third party import "azure.core.exceptions.ServiceRequestError" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:22:0: C0411: third party import "elipy2.expire" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:32:0: C0411: third party import "elipy2.azcopy_client.AZCopyClient" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:33:0: C0411: third party import "elipy2.exceptions.ELIPYException" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:34:0: C0411: third party import "elipy2.cli.pass_context" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)
c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\deleter.py:35:0: C0411: third party import "elipy2.telemetry.collect_metrics" should be placed before first party imports "dice_elipy_scripts.utils.decorators.throw_if_files_found", "dice_elipy_scripts.utils.delete_utils.delete_empty_folders", "dice_elipy_scripts.utils.sentry_utils.add_sentry_tags"  (wrong-import-order)


Report
======
355 statements analysed.

Statistics by type
------------------

+---------+-------+-----------+-----------+------------+---------+
|type     |number |old number |difference |%documented |%badname |
+=========+=======+===========+===========+============+=========+
|module   |1      |1          |=          |100.00      |0.00     |
+---------+-------+-----------+-----------+------------+---------+
|class    |0      |NC         |NC         |0           |0        |
+---------+-------+-----------+-----------+------------+---------+
|method   |0      |NC         |NC         |0           |0        |
+---------+-------+-----------+-----------+------------+---------+
|function |23     |23         |=          |100.00      |0.00     |
+---------+-------+-----------+-----------+------------+---------+



External dependencies
---------------------
::

    azure 
      \-core 
        \-exceptions (dice_elipy_scripts.deleter)
    click (dice_elipy_scripts.deleter)
    elipy2 (dice_elipy_scripts.deleter)
      \-avalanche (dice_elipy_scripts.deleter)
      \-az_utils (dice_elipy_scripts.deleter)
      \-azcopy_client (dice_elipy_scripts.deleter)
      \-build_metadata_utils (dice_elipy_scripts.deleter)
      \-cli (dice_elipy_scripts.deleter)
      \-core (dice_elipy_scripts.deleter)
      \-exceptions (dice_elipy_scripts.deleter)
      \-expire (dice_elipy_scripts.deleter)
      \-symbols (dice_elipy_scripts.deleter)
      \-telemetry (dice_elipy_scripts.deleter)
    six (dice_elipy_scripts.deleter)



791 lines have been analyzed

Raw metrics
-----------

+----------+-------+------+---------+-----------+
|type      |number |%     |previous |difference |
+==========+=======+======+=========+===========+
|code      |573    |72.44 |573      |=          |
+----------+-------+------+---------+-----------+
|docstring |88     |11.13 |88       |=          |
+----------+-------+------+---------+-----------+
|comment   |31     |3.92  |30       |*****      |
+----------+-------+------+---------+-----------+
|empty     |99     |12.52 |99       |=          |
+----------+-------+------+---------+-----------+



Duplication
-----------

+-------------------------+------+---------+-----------+
|                         |now   |previous |difference |
+=========================+======+=========+===========+
|nb duplicated lines      |0     |0        |0          |
+-------------------------+------+---------+-----------+
|percent duplicated lines |0.000 |0.000    |=          |
+-------------------------+------+---------+-----------+



Messages by category
--------------------

+-----------+-------+---------+-----------+
|type       |number |previous |difference |
+===========+=======+=========+===========+
|convention |33     |34       |34         |
+-----------+-------+---------+-----------+
|refactor   |5      |5        |5          |
+-----------+-------+---------+-----------+
|warning    |25     |25       |25         |
+-----------+-------+---------+-----------+
|error      |0      |0        |0          |
+-----------+-------+---------+-----------+



% errors / warnings by module
-----------------------------

+---------------------------+------+--------+---------+-----------+
|module                     |error |warning |refactor |convention |
+===========================+======+========+=========+===========+
|dice_elipy_scripts.deleter |0.00  |100.00  |100.00   |100.00     |
+---------------------------+------+--------+---------+-----------+



Messages
--------

+---------------------------------------+------------+
|message id                             |occurrences |
+=======================================+============+
|consider-using-f-string                |18          |
+---------------------------------------+------------+
|logging-format-interpolation           |15          |
+---------------------------------------+------------+
|wrong-import-order                     |14          |
+---------------------------------------+------------+
|broad-exception-caught                 |8           |
+---------------------------------------+------------+
|too-many-locals                        |3           |
+---------------------------------------+------------+
|too-many-arguments                     |2           |
+---------------------------------------+------------+
|raise-missing-from                     |2           |
+---------------------------------------+------------+
|use-implicit-booleaness-not-comparison |1           |
+---------------------------------------+------------+




------------------------------------------------------------------
Your code has been rated at 8.23/10 (previous run: 8.20/10, +0.03)

