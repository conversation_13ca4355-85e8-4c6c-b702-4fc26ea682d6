#!/usr/bin/env python3
"""
Test script to verify the sanitizers branch discovery fix
"""
import os
import sys

# Add the path to the elipy-scripts module
sys.path.insert(0, r'C:\Users\<USER>\vscode\pycharm\elipy-scripts')

from dice_elipy_scripts.deleter import _scan_code_branches

def test_sanitizers_branch_discovery():
    """Test the updated _scan_code_branches function with sanitizers path"""
    
    # Test path for sanitizers
    sanitizers_path = r"\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers"
    
    print(f"Testing sanitizers branch discovery for: {sanitizers_path}")
    print("=" * 80)
    
    try:
        branches = _scan_code_branches(sanitizers_path)
        print(f"Found {len(branches)} branches:")
        for branch in sorted(branches):
            print(f"  - {branch}")
            
        # Verify we found the expected branches
        expected_branches = {"ch1-content-dev-sanitizers/asan", "ch1-content-dev-sanitizers/ubsan"}
        found_branches = set(branches)
        
        print("\nExpected branches:", expected_branches)
        print("Found branches:", found_branches)
        
        if expected_branches.issubset(found_branches):
            print("✅ SUCCESS: All expected sanitizers branches found!")
        else:
            missing = expected_branches - found_branches
            print(f"❌ MISSING: {missing}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

def test_regular_code_branch_discovery():
    """Test the updated function still works for regular code paths"""
    
    # Test with a regular code path
    regular_path = r"\\filer.dice.ad.ea.com\builds\Battlefield\code"
    
    print(f"\nTesting regular code branch discovery for: {regular_path}")
    print("=" * 80)
    
    try:
        branches = _scan_code_branches(regular_path)
        print(f"Found {len(branches)} branches (showing first 10):")
        for branch in sorted(list(branches)[:10]):
            print(f"  - {branch}")
            
        if len(branches) > 10:
            print(f"  ... and {len(branches) - 10} more")
            
        # Check that we found some expected branches
        expected_in_branches = ["ch1-bflabs-release", "ch1-content-dev", "ch1-content-dev-sanitizers"]
        found_any = any(branch in branches for branch in expected_in_branches)
        
        if found_any:
            print("✅ SUCCESS: Found expected regular branches!")
        else:
            print("❌ No expected regular branches found")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing sanitizers branch discovery fix")
    print("=" * 80)
    
    test_sanitizers_branch_discovery()
    test_regular_code_branch_discovery()
    
    print("\nTest completed!")
