["elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestAvalanche::test_init", "elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestDropCacheBucket::test_drop_cache_bucket", "elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestDropCacheBucket::test_drop_cache_bucket_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestGetCacheValue::test_put_cache_bucket", "elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestGetCacheValue::test_put_cache_bucket_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestPutCacheBucket::test_put_cache_bucket", "elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestPutCacheBucket::test_put_cache_bucket_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestTriggerGc::test_get_cache_summary", "elipy2/tests/avalanche_web_api/test_avalanche_cache.py::TestTriggerGc::test_get_cache_summary_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCore::test_connect", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCore::test_disconnect", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCore::test_init", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url[/some-http://test-av-server.test.dre.dice.se/some]", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url[/some/nested-http://test-av-server.test.dre.dice.se/some/nested]", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url[/some/nested/url-http://test-av-server.test.dre.dice.se/some/nested/url]", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url[some-http://test-av-server.test.dre.dice.se/some]", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestCreateUrl::test_create_url_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestDelete::test_delete", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestDelete::test_delete_bad_error_code_lower_bound", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestDelete::test_delete_bad_error_code_upper_bound", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestDelete::test_delete_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get_bad_url", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get_connection_error", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get_connection_timeout", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestGet::test_get_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPost::test_post", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPost::test_post_bad_error_code_lower_bound", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPost::test_post_bad_error_code_upper_bound", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPost::test_post_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPut::test_put", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPut::test_put_bad_error_code_lower_bound", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPut::test_put_bad_error_code_upper_bound", "elipy2/tests/avalanche_web_api/test_avalanche_core.py::TestPut::test_put_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExists::test_db_exists_false", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExists::test_db_exists_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExists::test_db_exists_true", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_false", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_string_false", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_string_true[BattlefieldGameData]", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_string_true[Data.kin]", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_string_true[ev.Win32.Debug]", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_true[BattlefieldGameData]", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_true[Data.kin]", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDbExistsWith::test_db_exists_with_true[ev.Win32.Debug]", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDeleteDatabase::test_delete_database", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDeleteDatabase::test_delete_database_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestDeleteDatabase::test_delete_empty_database", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetBuiltLevels::test_get_built_levels", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetBuiltLevels::test_get_built_levels_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetDb::test_get_db", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetDb::test_get_db_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetDbAll::test_get_db", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestGetDbAll::test_get_db_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestPutBuiltLevels::test_put_built_levels", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestPutBuiltLevels::test_put_built_levels_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_database.py::TestStorage::test_init", "elipy2/tests/avalanche_web_api/test_avalanche_server.py::TestGetStatus::test_get_status", "elipy2/tests/avalanche_web_api/test_avalanche_server.py::TestGetStatus::test_get_status_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_server.py::TestStorage::test_components", "elipy2/tests/avalanche_web_api/test_avalanche_server.py::TestStorage::test_init", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestAvalanche::test_init", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestEvacuateOverflow::test_evacuate_overflow", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestForceGc::test_force_gc", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestForceGc::test_force_gc_async", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestForceGc::test_force_gc_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestGetPoolInfo::test_get_pool_info", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestGetPoolInfo::test_get_pool_info_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestPoolPrimaryInfo::test_get_pool_primary_info", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestPoolPrimaryInfo::test_get_pool_primary_info_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestRebalanace::test_rebalance", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestSizeOfFmt::test_sizeof_fmt", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTriggerGc::test_trigger_gc", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTriggerGc::test_trigger_gc_async", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTriggerGc::test_trigger_gc_force", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTriggerGc::test_trigger_gc_return_type", "elipy2/tests/avalanche_web_api/test_avalanche_storage.py::TestTrimExtents::test_trim_extents", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestDumpAttributes::test_dump_attributes", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestDumpAttributes::test_dump_attributes_retry[IndexError]", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestDumpAttributes::test_dump_attributes_retry[PermissionError]", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAttributes::test_filtered_data[code]", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAttributes::test_filtered_data[drone]", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAttributes::test_get_attributes", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAttributes::test_no_verified_data", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAutotestCategoryUuid::test_get_autotest_category_uuid", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAutotestCategoryUuid::test_get_autotest_category_uuid_02", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAutotestCategoryUuid::test_get_autotest_category_uuid_upper_and_lower_match", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetAutotestCategoryUuid::test_get_autotest_category_uuid_upper_case", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBasicDataFromBuildJson::test_get_basic_data_from_build_json_matching_attributes", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBasicDataFromBuildJson::test_get_basic_data_from_build_json_matching_verified_len", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBasicDataFromBuildJson::test_get_basic_data_from_build_json_original_unmodified", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBuildDataFromBuildJson::test_get_build_data_from_build_json", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBySource::test_get_by_source", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBySource::test_get_by_source_default", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetBySource::test_get_by_source_include_deleted", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetByUuid::test_get_by_uuid", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetCategoryDataBySourceUuid::test_get_category_data_by_source_uuid", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetFlattenedAutotestData::test_get_test_data_from_build_json", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetFlattenedAutotestData::test_get_testcategory_data_from_build_json", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_categories_data_match", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_individualtest_data_match", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_openbetatests_data_match", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_test_category_data_match", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_test_data_match", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetInflattedAutotestData::test_get_flattened_autotest_data_test_platform_data_match", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetTestDataUuid::test_get_autotest_test_uuid", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetTestDataUuid::test_get_autotest_test_uuid_upper_and_lower_match", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetTestDataUuid::test_get_autotest_test_uuid_upper_case", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetVerifiedDataFromTestData::test_get_verified_data_from_test_data_mismatch", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestGetVerifiedDataFromTestData::test_get_verified_data_from_test_data_should_not_raise", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::TestRegisterAutotestCategoryData::test_register_autotest_category_data", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::TestRegisterAutotestCategoryData::test_register_autotest_category_data_dump_attributes[False-0]", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::TestRegisterAutotestCategoryData::test_register_autotest_category_data_dump_attributes[True-1]", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_build", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_build_body", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_build_dump_attributes[False-0]", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_build_dump_attributes[True-1]", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterAutotestBuild::test_register_autotest_test_data", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterBuildData::test_register_build_data", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterBuildData::test_register_build_data_no_key_verified_data", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterBuildData::test_register_build_data_passed_data", "elipy2/tests/bilbo_v2/test_bilbo_v2.py::TestRegisterBuildData::test_register_code_build_data", "elipy2/tests/test_artifactory_client.py::TestArtifactoryClient::test_does_not_download_when_exists", "elipy2/tests/test_artifactory_client.py::TestArtifactoryClient::test_download_zip_artifact", "elipy2/tests/test_artifactory_client.py::TestArtifactoryClient::test_downloads_when_force_download_is_true", "elipy2/tests/test_artifactory_client.py::TestArtifactoryClient::test_returns_correct_path_when_downloading_file", "elipy2/tests/test_avalanche.py::TestAvalanche::test__http_delete", "elipy2/tests/test_avalanche.py::TestAvalanche::test__http_delete_bad_status", "elipy2/tests/test_avalanche.py::TestAvalanche::test__http_get", "elipy2/tests/test_avalanche.py::TestAvalanche::test__http_get_bad_status", "elipy2/tests/test_avalanche.py::TestAvalanche::test__http_put", "elipy2/tests/test_avalanche.py::TestAvalanche::test__http_put_bad_status", "elipy2/tests/test_avalanche.py::TestAvalanche::test__http_put_default_header", "elipy2/tests/test_avalanche.py::TestAvalanche::test__http_put_set_header", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_blocker_check", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_blocker_check_multiple", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_blocker_check_no_relevant_process", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_blocker_check_not_running", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_invoke", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_high_space", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_low_space", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_medium_space", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_not_running", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_ret_failed", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_running", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_maintenance_running_nodb", "elipy2/tests/test_avalanche.py::TestAvalanche::test_avalanche_status", "elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_api_when_not_return", "elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_api_when_not_running", "elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_api_when_running", "elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_when_not_running", "elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_service_when_running", "elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_space", "elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_space_failure", "elipy2/tests/test_avalanche.py::TestAvalanche::test_check_avalanche_space_overflow", "elipy2/tests/test_avalanche.py::TestAvalanche::test_clean_avalanche", "elipy2/tests/test_avalanche.py::TestAvalanche::test_clean_empty_dbs", "elipy2/tests/test_avalanche.py::TestAvalanche::test_clean_old_dbs", "elipy2/tests/test_avalanche.py::TestAvalanche::test_clean_old_dbs_long_storage_time", "elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_cleanup", "elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_cleanup_avalanche_space", "elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_no_cleanup", "elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_no_cleanup_too_few_dbs", "elipy2/tests/test_avalanche.py::TestAvalanche::test_cleanup_temp_dbs_no_cleanup_too_must_space", "elipy2/tests/test_avalanche.py::TestAvalanche::test_clone_db", "elipy2/tests/test_avalanche.py::TestAvalanche::test_combine", "elipy2/tests/test_avalanche.py::TestAvalanche::test_combine_extra_args", "elipy2/tests/test_avalanche.py::TestAvalanche::test_db_contains", "elipy2/tests/test_avalanche.py::TestAvalanche::test_db_exists", "elipy2/tests/test_avalanche.py::TestAvalanche::test_ddelta", "elipy2/tests/test_avalanche.py::TestAvalanche::test_ddelta_is_path", "elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_create_dir", "elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_dir_exists", "elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_new_dest", "elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_new_dest_extra_args", "elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_no_ordering_algorithm", "elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_non_default_ordering_algorithm", "elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_skip_platform", "elipy2/tests/test_avalanche.py::TestAvalanche::test_deploy_skip_platform_failure", "elipy2/tests/test_avalanche.py::TestAvalanche::test_drop", "elipy2/tests/test_avalanche.py::TestAvalanche::test_drop_all_dbs", "elipy2/tests/test_avalanche.py::TestAvalanche::test_drop_build_record", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state_deploy_state_to_bundles", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state_exception", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state_old_fb_version", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export_avalanche_state_remote_clone_db", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export_is_path", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export_noobject_scenario_1", "elipy2/tests/test_avalanche.py::TestAvalanche::test_export_noobject_scenario_2", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_autotest_temp_db_name", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_db", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_db_default", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_db_valid", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_db_valid_default", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_platform_name", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_avalanche_platform_name_reverse", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_built_levels", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_built_levels_create_path", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_built_levels_path_exists", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_built_levels_with_open", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_cache_value", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_cache_value_list", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_database_id", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_database_id_not_found", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_all_data", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_fb2018", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_fb2019_pr7", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_fb2019_pr7_server", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_prefix", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_prefix_fb2018", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_db_name_prefix_fb2019_pr7", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_fb_branch_id", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_fb_branch_id_not_found", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_full_database_name", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_local_avalanche", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_ops_chain_exception", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_ops_chain_with_open", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_short_database_name", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_temp_db_name", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_temp_db_name_2018", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_temp_db_name_2019_pr7", "elipy2/tests/test_avalanche.py::TestAvalanche::test_get_temp_db_name_custom_prefix", "elipy2/tests/test_avalanche.py::TestAvalanche::test_getdbs", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_already_imported", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_skip_db_exists", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_skip_no_new_code", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_skip_reimport_not_needed", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_success", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_avalanche_state_success_with_data", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_baselinedb", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_baselinedb_cleanup_continue", "elipy2/tests/test_avalanche.py::TestAvalanche::test_import_baselinedb_cleanup_stop", "elipy2/tests/test_avalanche.py::TestAvalanche::test_importdb", "elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke", "elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke_exception", "elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke_exception_failure_dbs_found", "elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke_exception_not_failing", "elipy2/tests/test_avalanche.py::TestAvalanche::test_nuke_timeout", "elipy2/tests/test_avalanche.py::TestAvalanche::test_put_built_levels", "elipy2/tests/test_avalanche.py::TestAvalanche::test_reimport_needed_exception", "elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_complete_clone", "elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_ephemeral_default", "elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_ephemeral_no_setting", "elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_ephemeral_with_bad_setting", "elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_ephemeral_with_setting", "elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_push_built_levels", "elipy2/tests/test_avalanche.py::TestAvalanche::test_remote_clone_db_with_source", "elipy2/tests/test_avalanche.py::TestAvalanche::test_restart_avalanche_avalanche_already_running", "elipy2/tests/test_avalanche.py::TestAvalanche::test_restart_avalanche_timeout", "elipy2/tests/test_avalanche.py::TestAvalanche::test_restart_avalanche_true", "elipy2/tests/test_avalanche.py::TestAvalanche::test_set_cache_value", "elipy2/tests/test_avalanche.py::TestAvalanche::test_set_cache_value_list", "elipy2/tests/test_avalanche.py::TestAvalanche::test_stop_avalanche_already_stopped", "elipy2/tests/test_avalanche.py::TestAvalanche::test_stop_avalanche_true", "elipy2/tests/test_avalanche.py::TestAvalancheAltLocation::test_export_avalanche_state_remote_clone_db_no_setting", "elipy2/tests/test_avalanche.py::TestAvalancheAltLocation::test_remote_clone_db_ephemeral_no_setting", "elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_reimport_needed", "elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_reimport_needed_db_doesnt_exist", "elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_reimport_needed_no_data_changelist", "elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_remote_clone_db", "elipy2/tests/test_avalanche.py::TestAvalancheCustomComputerName::test_set_avalanche_build_status", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_maintenance", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_maintenance_file_not_found", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_maintenance_non_windows", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_upstream_sets", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_disable_upstream_sets_non_windows", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_set_upstream_node", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_set_upstream_node_non_windows", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_update_storage_settings", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_update_storage_settings_non_windows", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_update_storage_settings_skip_path", "elipy2/tests/test_avalanche.py::TestAvalancheRegistryKeyUpdate::test_update_storage_settings_skip_size", "elipy2/tests/test_az_utils.py::test_get_azure_fileshare_credentials", "elipy2/tests/test_az_utils.py::test_get_azure_fileshare_sas_token_called_with_correct_parameters", "elipy2/tests/test_az_utils.py::test_get_offset_datetime", "elipy2/tests/test_az_utils.py::test_yield_azfileshare_path_contents_metadata_is_a_generator", "elipy2/tests/test_az_utils.py::test_yield_azfileshare_path_contents_metadata_yields_str_str_dicts", "elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_copy_with_default_args", "elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_copy_with_non_default_args", "elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_init_azcopy_correct_version", "elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_init_azcopy_is_not_installed", "elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_init_azcopy_non_default_args", "elipy2/tests/test_azcopy_client.py::TestAZCopyClient::test_init_azcopy_not_tested_version", "elipy2/tests/test_bilbo.py::TestBilbo::test_delete_build", "elipy2/tests/test_bilbo.py::TestBilbo::test_dump_attributes_NoneAttributes", "elipy2/tests/test_bilbo.py::TestBilbo::test_dump_attributes_lower_case", "elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path", "elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path_empth", "elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path_one", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_aggregations_not_found", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_aggregations_success", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds_query_string_not_found_handled", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds_query_string_success", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_build_by_id_success", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_build_ids", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_builds_matching", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_builds_with_query_success", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_last_successful", "elipy2/tests/test_bilbo.py::TestBilbo::test_get_last_successful_no", "elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_do_not_update", "elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_first_time", "elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_handle_race_condition", "elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_update_is_use_until", "elipy2/tests/test_bilbo.py::TestBilbo::test_not_set_branch_changelist_no_update_bilbo", "elipy2/tests/test_bilbo.py::TestBilbo::test_not_set_verified_data_block_no_update_bilbo", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_as_used", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_category_exists_but_test_has_not_been_created", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_fail", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_handle_race_condition", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_register_new_test_on_existing_test", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_run_first_time", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_update_test_status", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_state", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_avalanche_state", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_calls__register_build_when_parameters_meet_reqs", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test0]", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test1]", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test2]", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test3]", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test4]", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles_no_path", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles_non_default_bundle_type", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_clone_host", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_code_branch", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_code_cl", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_data_branch", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_data_cl", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_dest_db", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_platform", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_code_build", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_data_smoke", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_drone_build", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_code_branch", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_code_cl", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_config", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_data_branch", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_data_cl", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_dataset", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_pkgtype", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_platform", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_with_combine_params", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_with_partial_combine_params", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols_build_no_branch", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols_build_no_cl", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_ant_build_no_branch", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_ant_build_no_cl", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_ant", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_no_branch", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_no_cl", "elipy2/tests/test_bilbo.py::TestBilbo::test_register_web_export", "elipy2/tests/test_bilbo.py::TestBilbo::test_set_attribute", "elipy2/tests/test_bilbo.py::TestBilbo::test_set_autotest_category_status", "elipy2/tests/test_bilbo.py::TestBilbo::test_set_autotest_category_status_category_not_found", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_build_as_release_candidate", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked_data", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked_fail", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_method", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_cl", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_path", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_verified_data", "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_no_matching_cl", "elipy2/tests/test_bilbo.py::TestBilboBuildJsonOverride::test_bilbo_build_json_default", "elipy2/tests/test_bilbo.py::TestBilboBuildJsonOverride::test_bilbo_build_json_override", "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes", "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes_NoneAttributes", "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes_retry", "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_aggregations", "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_all", "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_call", "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_call_failure", "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_with_query", "elipy2/tests/test_bilbo.py::TestBilboElasticSearchMetadataProvider::test_dump_attributes", "elipy2/tests/test_bilbo.py::TestBilboElasticSearchMetadataProvider::test_dump_attributes_NoneAttributes", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_get_uuid", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_get_uuid_uppercase", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_get_uuid_uppercase_and_lowercase_donot_match", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_build_failures[register_func0]", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_build_failures[register_func1]", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_code_branch", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_code_cl", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_data_branch", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_data_cl", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_platform", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_no_branch_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_no_changelist_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_code_branch_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_code_changelist_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_data_branch_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_data_changelist_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_dataset_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_retry_update_dump_attributes_exception", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_retry_update_existing_build", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_retry_update_new_build", "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_set_attribute", "elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_code_build_exception", "elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_drone_build_code_changelist_exception", "elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_drone_build_data_changelist_exception", "elipy2/tests/test_bilbo_es_helper.py::TestBilboElasticSearchHelper::test_get_newest_with_query_string", "elipy2/tests/test_bilbo_es_helper.py::TestBilboElasticSearchHelper::test_get_newest_with_query_string_4_pages", "elipy2/tests/test_bilbo_es_helper.py::TestBilboElasticSearchHelper::test_get_newest_with_query_string_extra_pages_no_hits", "elipy2/tests/test_bilbo_utils.py::TestGetAllBuilds::test_get_all_builds_custom_query", "elipy2/tests/test_build_metadata.py::test_create", "elipy2/tests/test_build_metadata.py::test_delete_build", "elipy2/tests/test_build_metadata.py::test_get_aggregations", "elipy2/tests/test_build_metadata.py::test_get_all_builds", "elipy2/tests/test_build_metadata.py::test_get_all_builds_query_string", "elipy2/tests/test_build_metadata.py::test_get_build_by_id", "elipy2/tests/test_build_metadata.py::test_get_build_ids", "elipy2/tests/test_build_metadata.py::test_get_builds_matching", "elipy2/tests/test_build_metadata.py::test_get_builds_with_query", "elipy2/tests/test_build_metadata.py::test_get_last_successful", "elipy2/tests/test_build_metadata.py::test_len", "elipy2/tests/test_build_metadata.py::test_mark_build_as_in_use_until", "elipy2/tests/test_build_metadata.py::test_register_ant_local_build", "elipy2/tests/test_build_metadata.py::test_register_as_used", "elipy2/tests/test_build_metadata.py::test_register_autotest_state", "elipy2/tests/test_build_metadata.py::test_register_avalanche_state", "elipy2/tests/test_build_metadata.py::test_register_baseline_build", "elipy2/tests/test_build_metadata.py::test_register_build[register_func0]", "elipy2/tests/test_build_metadata.py::test_register_build[register_func1]", "elipy2/tests/test_build_metadata.py::test_register_build[register_func2]", "elipy2/tests/test_build_metadata.py::test_register_build_data", "elipy2/tests/test_build_metadata.py::test_register_bundles", "elipy2/tests/test_build_metadata.py::test_register_bundles_alt_type", "elipy2/tests/test_build_metadata.py::test_register_clone_db", "elipy2/tests/test_build_metadata.py::test_register_code_build", "elipy2/tests/test_build_metadata.py::test_register_drone_build", "elipy2/tests/test_build_metadata.py::test_register_expression_debug_data", "elipy2/tests/test_build_metadata.py::test_register_frosty_build", "elipy2/tests/test_build_metadata.py::test_register_frosty_build_with_combine_parameters", "elipy2/tests/test_build_metadata.py::test_register_symbols", "elipy2/tests/test_build_metadata.py::test_register_tnt_local_build", "elipy2/tests/test_build_metadata.py::test_register_web_export", "elipy2/tests/test_build_metadata.py::test_set_attribute", "elipy2/tests/test_build_metadata.py::test_set_autotest_category_status", "elipy2/tests/test_build_metadata.py::test_tag_build_as_release_candidate", "elipy2/tests/test_build_metadata.py::test_tag_code_build_as_smoked", "elipy2/tests/test_cli.py::TestCli::test_cli", "elipy2/tests/test_cli.py::TestCli::test_cli_plugin_folder_duplicate", "elipy2/tests/test_cli.py::TestCli::test_cli_without_plugin_folder", "elipy2/tests/test_code.py::TestCode::test_add_files_to_pushbuild", "elipy2/tests/test_code.py::TestCode::test_add_files_to_pushbuild_no_platform", "elipy2/tests/test_code.py::TestCode::test_add_files_to_pushbuild_tool", "elipy2/tests/test_code.py::TestCode::test_buildsln_fbcli", "elipy2/tests/test_code.py::TestCode::test_buildsln_fbenv_framework_args", "elipy2/tests/test_code.py::TestCode::test_buildsln_fbenv_new", "elipy2/tests/test_code.py::TestCode::test_buildsln_fbenv_old", "elipy2/tests/test_code.py::TestCode::test_buildsln_fbenv_override_config", "elipy2/tests/test_code.py::TestCode::test_buildsln_notfailonfirst", "elipy2/tests/test_code.py::TestCode::test_buildsln_tool_with_exception", "elipy2/tests/test_code.py::TestCode::test_buildsln_tools_fbenv", "elipy2/tests/test_code.py::TestCode::test_buildsln_with_exception", "elipy2/tests/test_code.py::TestCode::test_clean_local", "elipy2/tests/test_code.py::TestCode::test_clean_local_failure", "elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_project_default", "elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_project_specific", "elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_server", "elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_win64", "elipy2/tests/test_code.py::TestCode::test_clean_platform_temp_files_xb1", "elipy2/tests/test_code.py::TestCode::test_constructor_p4_config_buildsystem_default", "elipy2/tests/test_code.py::TestCode::test_constructor_p4_config_buildsystem_dont_overwrite_p4config", "elipy2/tests/test_code.py::TestCode::test_constructor_p4_config_local", "elipy2/tests/test_code.py::TestCode::test_constructor_with_exception", "elipy2/tests/test_code.py::TestCode::test_gensln_fbcli", "elipy2/tests/test_code.py::TestCode::test_gensln_fbcli_wsl", "elipy2/tests/test_code.py::TestCode::test_gensln_fbenv", "elipy2/tests/test_code.py::TestCode::test_gensln_fbenv_2018", "elipy2/tests/test_code.py::TestCode::test_gensln_fbenv_override_config", "elipy2/tests/test_code.py::TestCode::test_gensln_with_exception", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_fake_platform", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_internal_gen4a", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_internal_ps4", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_internal_win64", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_pc_fail", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_project_default", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_project_specific", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_win64", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_xb1", "elipy2/tests/test_code.py::TestCode::test_increment_client_version_xb1_fail", "elipy2/tests/test_code.py::TestCode::test_nantonpackage", "elipy2/tests/test_code.py::TestCode::test_nantonpackage_all_config_new_fb", "elipy2/tests/test_code.py::TestCode::test_nantonpackage_all_config_old_fb", "elipy2/tests/test_code.py::TestCode::test_nantonpackage_debug_config_new_fb", "elipy2/tests/test_code.py::TestCode::test_nantonpackage_debug_config_old_fb", "elipy2/tests/test_code.py::TestCode::test_nantonpackage_release_config_new_fb", "elipy2/tests/test_code.py::TestCode::test_nantonpackage_release_config_old_fb", "elipy2/tests/test_code.py::TestCode::test_nantonpackage_with_exception", "elipy2/tests/test_code.py::TestCode::test_nantonpackage_with_framework_args", "elipy2/tests/test_code.py::TestCode::test_pkgprebuilds_exception", "elipy2/tests/test_code.py::TestCode::test_pkgprebuilds_fw_arg", "elipy2/tests/test_code.py::TestCode::test_pkgprebuilds_platform_as_list", "elipy2/tests/test_code.py::TestCode::test_pkgprebuilds_platform_as_str", "elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy", "elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_no_excluded_file", "elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_no_file_path", "elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_no_platform", "elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_plt", "elipy2/tests/test_code.py::TestCode::test_remove_files_for_eacopy_specified_file_path", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_context_multiple_commands", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_finish_cmd_log", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_init", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_read_data_no_file_exists", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_sig_handler", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_start_cmd_log", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_start_cmd_log_executor", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_start_cmd_log_task_id", "elipy2/tests/test_command_logger.py::TestCommandLogger::test_write_and_read_data_no_file_exists", "elipy2/tests/test_command_logger.py::TestCommandLoggerRunTests::test_run_execute_cmd_file", "elipy2/tests/test_config.py::TestConfigModule::test_get_setting", "elipy2/tests/test_config.py::TestConfigModule::test_get_setting_invalid_location_exception", "elipy2/tests/test_config.py::TestConfigModule::test_get_setting_invalid_value_exception", "elipy2/tests/test_config.py::TestConfigModule::test_get_with_default", "elipy2/tests/test_config.py::TestConfigModule::test_get_with_default_none", "elipy2/tests/test_config.py::TestConfigModule::test_load_config", "elipy2/tests/test_config.py::TestConfigModule::test_path_get", "elipy2/tests/test_config.py::TestConfigModule::test_resolve_setting", "elipy2/tests/test_config.py::TestConfigModuleCustomPath::test_get_default_location", "elipy2/tests/test_config.py::TestConfigModuleCustomPath::test_get_production_location", "elipy2/tests/test_core.py::TestCoreModule::test__delete_folder_callback_listdir", "elipy2/tests/test_core.py::TestCoreModule::test__delete_folder_listdir", "elipy2/tests/test_core.py::TestCoreModule::test_check_for_handles", "elipy2/tests/test_core.py::TestCoreModule::test_clean_temp", "elipy2/tests/test_core.py::TestCoreModule::test_clean_temp_exe_allow", "elipy2/tests/test_core.py::TestCoreModule::test_close_file_handles", "elipy2/tests/test_core.py::TestCoreModule::test_close_file_handles_fail", "elipy2/tests/test_core.py::TestCoreModule::test_close_file_handles_no_open", "elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles", "elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_api_not_available", "elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_invalid_path", "elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_jsondecode_exception_failure", "elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_jsondecode_exception_success", "elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_jsondecode_retry_count", "elipy2/tests/test_core.py::TestCoreModule::test_close_filer_file_handles_no_open_handle", "elipy2/tests/test_core.py::TestCoreModule::test_create_zip", "elipy2/tests/test_core.py::TestCoreModule::test_create_zip_extra_args", "elipy2/tests/test_core.py::TestCoreModule::test_create_zip_failure_wrong_file_extension", "elipy2/tests/test_core.py::TestCoreModule::test_create_zip_makedirs", "elipy2/tests/test_core.py::TestCoreModule::test_create_zip_network_share_path", "elipy2/tests/test_core.py::TestCoreModule::test_create_zip_skip_compression", "elipy2/tests/test_core.py::TestCoreModule::test_create_zip_skip_compression_network_share_path", "elipy2/tests/test_core.py::TestCoreModule::test_decode_line_utf16le", "elipy2/tests/test_core.py::TestCoreModule::test_decode_line_utf8", "elipy2/tests/test_core.py::TestCoreModule::test_delete_file_fake", "elipy2/tests/test_core.py::TestCoreModule::test_delete_file_real", "elipy2/tests/test_core.py::TestCoreModule::test_delete_filer_folder", "elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_real", "elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_real_deeper", "elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_with_robocopy", "elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_with_robocopy_already_exists_os_cached", "elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_with_robocopy_empty", "elipy2/tests/test_core.py::TestCoreModule::test_delete_folder_with_robocopy_exe", "elipy2/tests/test_core.py::TestCoreModule::test_ensure_p4_config", "elipy2/tests/test_core.py::TestCoreModule::test_ensure_p4_config_None", "elipy2/tests/test_core.py::TestCoreModule::test_ensure_package_is_installed", "elipy2/tests/test_core.py::TestCoreModule::test_ensure_package_is_installed_pre2022", "elipy2/tests/test_core.py::TestCoreModule::test_ensure_package_is_installed_with_bool", "elipy2/tests/test_core.py::TestCoreModule::test_extract_zip", "elipy2/tests/test_core.py::TestCoreModule::test_extract_zip_failure_run_7zip", "elipy2/tests/test_core.py::TestCoreModule::test_extract_zip_failure_wrong_file_extension", "elipy2/tests/test_core.py::TestCoreModule::test_get_counter_value", "elipy2/tests/test_core.py::TestCoreModule::test_get_licensee_code_folder", "elipy2/tests/test_core.py::TestCoreModule::test_get_licensee_code_folder_exception", "elipy2/tests/test_core.py::TestCoreModule::test_is_buildsystem_run", "elipy2/tests/test_core.py::TestCoreModule::test_is_filer_api_available", "elipy2/tests/test_core.py::TestCoreModule::test_list_chunk_generator", "elipy2/tests/test_core.py::TestCoreModule::test_list_chunk_generator_short_list", "elipy2/tests/test_core.py::TestCoreModule::test_md5_check", "elipy2/tests/test_core.py::TestCoreModule::test_md5_check_fail", "elipy2/tests/test_core.py::TestCoreModule::test_md5_check_fail_missing", "elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder", "elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_default_chunk_size", "elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_multiple_folder_levels", "elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_specified_chunk_size", "elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_txt", "elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_folder_undefined_file_type", "elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_one_file", "elipy2/tests/test_core.py::TestCoreModule::test_md5_hash_one_file_parallel", "elipy2/tests/test_core.py::TestCoreModule::test_robocopy", "elipy2/tests/test_core.py::TestCoreModule::test_robocopy_args", "elipy2/tests/test_core.py::TestCoreModule::test_run_7zip", "elipy2/tests/test_core.py::TestCoreModule::test_run_7zip_failure", "elipy2/tests/test_core.py::TestCoreModule::test_run_age_store", "elipy2/tests/test_core.py::TestCoreModule::test_run_age_store_dryrun", "elipy2/tests/test_core.py::TestCoreModule::test_run_async_join", "elipy2/tests/test_core.py::TestCoreModule::test_run_async_stop_all_mocked", "elipy2/tests/test_core.py::TestCoreModule::test_run_async_stop_all_normal", "elipy2/tests/test_core.py::TestCoreModule::test_update_shell", "elipy2/tests/test_core.py::TestCoreModule::test_use_bilbo", "elipy2/tests/test_core.py::TestCoreModule::test_write_output", "elipy2/tests/test_core.py::TestCoreModule::test_write_output_allow_exception", "elipy2/tests/test_core.py::TestCoreModule::test_write_output_with_file_io", "elipy2/tests/test_core.py::TestCoreModule::test_write_stderr", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_check_for_handles_ex", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_create_zip_missing_extension", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_robocopy_exception", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_robocopy_exception_missing_source", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_cmd_types", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_exception_not_list", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_non_zero", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_non_zero_allow", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_run_non_zero_eapm", "elipy2/tests/test_core.py::TestCoreModuleExceptions::test_use_bilbo_exe", "elipy2/tests/test_core.py::TestGetBuildTempFolderPath::test_get_build_temp_folder_path", "elipy2/tests/test_core.py::TestGetBuildTempFolderPath::test_get_build_temp_folder_path_jenkins", "elipy2/tests/test_core.py::TestGetLogDirectoryPath::test_get_log_directory_path_ado", "elipy2/tests/test_core.py::TestGetLogDirectoryPath::test_get_log_directory_path_jenkins", "elipy2/tests/test_core.py::TestGetLogDirectoryPath::test_get_log_directory_path_local", "elipy2/tests/test_core.py::TestGetLogFilePath::test_get_log_file_path_without_env", "elipy2/tests/test_core.py::TestPatchEnviron::test_env_changes", "elipy2/tests/test_core.py::TestPatchEnviron::test_env_reverts", "elipy2/tests/test_core.py::TestUnderscoreRun::test_run", "elipy2/tests/test_core.py::TestUnderscoreRun::test_variable_expansion", "elipy2/tests/test_core.py::TestUnderscoreRun::test_wait_not_called", "elipy2/tests/test_custom_logging.py::test_ado_logger", "elipy2/tests/test_custom_logging.py::test_default_logger", "elipy2/tests/test_custom_logging.py::test_logger_command", "elipy2/tests/test_custom_logging.py::test_logger_end_group", "elipy2/tests/test_custom_logging.py::test_logger_error", "elipy2/tests/test_custom_logging.py::test_logger_section", "elipy2/tests/test_custom_logging.py::test_logger_start_group", "elipy2/tests/test_custom_logging.py::test_set_level", "elipy2/tests/test_data.py::TestData::test_chunk_cooking_few_assets", "elipy2/tests/test_data.py::TestData::test_chunk_cooking_many_assets", "elipy2/tests/test_data.py::TestData::test_clean_ant_local", "elipy2/tests/test_data.py::TestData::test_clean_ant_local_dont_recreate_file_if_existing", "elipy2/tests/test_data.py::TestData::test_clean_ant_local_no_file", "elipy2/tests/test_data.py::TestData::test_clean_extra_pipeline_args", "elipy2/tests/test_data.py::TestData::test_clean_fbenv", "elipy2/tests/test_data.py::TestData::test_clean_local_datastate", "elipy2/tests/test_data.py::TestData::test_clean_reimport_with_platform_arg", "elipy2/tests/test_data.py::TestData::test_clean_reimport_without_platform_arg", "elipy2/tests/test_data.py::TestData::test_clear_cache_extra_args", "elipy2/tests/test_data.py::TestData::test_clear_cache_no_args", "elipy2/tests/test_data.py::TestData::test_cook_asset_clean_master", "elipy2/tests/test_data.py::TestData::test_cook_clean_index", "elipy2/tests/test_data.py::TestData::test_cook_fbcli", "elipy2/tests/test_data.py::TestData::test_cook_fbcli_only_indexing", "elipy2/tests/test_data.py::TestData::test_cook_fbcli_skip_indexing", "elipy2/tests/test_data.py::TestData::test_cook_fbenv", "elipy2/tests/test_data.py::TestData::test_cook_few_assets", "elipy2/tests/test_data.py::TestData::test_cook_few_assets_content_layer", "elipy2/tests/test_data.py::TestData::test_cook_many_assets", "elipy2/tests/test_data.py::TestData::test_cook_response_file", "elipy2/tests/test_data.py::TestData::test_cook_with_disable_caches", "elipy2/tests/test_data.py::TestData::test_cook_with_exception", "elipy2/tests/test_data.py::TestData::test_cook_with_exception_and_flag_calls_copy_mdmp_to_filer", "elipy2/tests/test_data.py::TestData::test_cook_with_exception_asset", "elipy2/tests/test_data.py::TestData::test_cook_with_exception_content_layer", "elipy2/tests/test_data.py::TestData::test_cook_with_exception_source_layer", "elipy2/tests/test_data.py::TestData::test_cook_with_exception_without_flag_doesnt_call_copy_mdmp_to_filer", "elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer", "elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer_create_dir_if_not_existing", "elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer_dont_create_dir_if_already_existing", "elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer_no_mdmp", "elipy2/tests/test_data.py::TestData::test_copy_mdmp_to_filer_only_move_mdmp_files", "elipy2/tests/test_data.py::TestData::test_data_get_content_layer_empty", "elipy2/tests/test_data.py::TestData::test_data_get_content_layer_end_case_insensitive", "elipy2/tests/test_data.py::TestData::test_data_get_content_layer_middle", "elipy2/tests/test_data.py::TestData::test_data_get_content_layer_missing", "elipy2/tests/test_data.py::TestData::test_data_get_content_layer_not_set", "elipy2/tests/test_data.py::TestData::test_extract_expression_debugdata", "elipy2/tests/test_data.py::TestData::test_extract_expression_debugdata_fail_bct", "elipy2/tests/test_data.py::TestData::test_extract_expression_debugdata_fail_not_bct", "elipy2/tests/test_data.py::TestData::test_extract_expression_debugdata_with_clean_args", "elipy2/tests/test_data.py::TestData::test_get_clean_master_version_args", "elipy2/tests/test_data.py::TestData::test_pipeline_args_default", "elipy2/tests/test_data.py::TestData::test_pipeline_args_include_arg", "elipy2/tests/test_data.py::TestData::test_pipeline_args_trim_already_included", "elipy2/tests/test_data.py::TestData::test_response_cooking", "elipy2/tests/test_data.py::TestData::test_response_file_exists_on_failure", "elipy2/tests/test_data.py::TestData::test_run_frostbite_data_dupgrade", "elipy2/tests/test_data.py::TestData::test_run_frostbite_data_upgrade_default", "elipy2/tests/test_data.py::TestData::test_run_frostbite_data_upgrade_extra_args", "elipy2/tests/test_data.py::TestData::test_run_guid_checker", "elipy2/tests/test_data.py::TestData::test_run_guid_checker_failure", "elipy2/tests/test_data.py::TestData::test_run_guid_checker_non_default_check_dir", "elipy2/tests/test_data.py::TestData::test_run_guid_checker_non_default_guid_checker_dir", "elipy2/tests/test_data.py::TestData::test_set_datadir", "elipy2/tests/test_data.py::TestData::test_skip_trim", "elipy2/tests/test_data.py::TestDataInit::test_data_init_buildfarm_default", "elipy2/tests/test_data.py::TestDataInit::test_data_init_buildfarm_monkey_build_label", "elipy2/tests/test_data.py::TestDataInit::test_data_init_buildfarm_skip_overwrite_p4config", "elipy2/tests/test_data.py::TestDataInit::test_data_init_local", "elipy2/tests/test_denuvo.py::TestDenuvo::test__get_file_paths", "elipy2/tests/test_denuvo.py::TestDenuvo::test__get_file_paths_file_in_unexpected_directory", "elipy2/tests/test_denuvo.py::TestDenuvo::test__get_file_paths_v2", "elipy2/tests/test_denuvo.py::TestDenuvo::test__get_version_specific_args_exclusions_path_does_not_exist", "elipy2/tests/test_denuvo.py::TestDenuvo::test_fetch_denuvo_files", "elipy2/tests/test_denuvo.py::TestDenuvo::test_fetch_denuvo_files_rerun", "elipy2/tests/test_denuvo.py::TestDenuvo::test_get_denuvo_program_args_absolute_exclusions_path", "elipy2/tests/test_denuvo.py::TestDenuvo::test_get_denuvo_program_args_no_quotes", "elipy2/tests/test_denuvo.py::TestDenuvo::test_get_denuvo_program_args_relative_exclusions_path", "elipy2/tests/test_denuvo.py::TestDenuvo::test_get_tnt_root", "elipy2/tests/test_denuvo.py::TestDenuvo::test_wrap", "elipy2/tests/test_denuvo.py::TestDenuvo::test_wrap_binary_path", "elipy2/tests/test_denuvo.py::TestDenuvo::test_wrap_denuvo_failed", "elipy2/tests/test_denuvo.py::TestDenuvo::test_wrap_v2", "elipy2/tests/test_enum_utils.py::TestEnumUtils::test_string_to_enum", "elipy2/tests/test_enum_utils.py::TestEnumUtils::test_string_to_enum_case_insenstive", "elipy2/tests/test_enum_utils.py::TestEnumUtils::test_string_to_enum_not_member", "elipy2/tests/test_expire.py::TestExpire::test_apply_retention_policies_no_rc_retention", "elipy2/tests/test_expire.py::TestExpire::test_apply_retention_policies_with_rc_retention", "elipy2/tests/test_expire.py::TestExpire::test_check_branch_retention_exceeded", "elipy2/tests/test_expire.py::TestExpire::test_check_branch_retention_valid", "elipy2/tests/test_expire.py::TestExpire::test_check_build_preservation_no_source", "elipy2/tests/test_expire.py::TestExpire::test_check_drone_preservation_exceeded", "elipy2/tests/test_expire.py::TestExpire::test_check_drone_preservation_valid", "elipy2/tests/test_expire.py::TestExpire::test_check_promoted_preservation_exceeded", "elipy2/tests/test_expire.py::TestExpire::test_check_promoted_preservation_valid", "elipy2/tests/test_expire.py::TestExpire::test_check_release_candidate_preservation_exceeded", "elipy2/tests/test_expire.py::TestExpire::test_check_release_candidate_preservation_valid", "elipy2/tests/test_expire.py::TestExpire::test_check_smoke_preservation_valid", "elipy2/tests/test_expire.py::TestExpire::test_check_spin_preservation_valid", "elipy2/tests/test_expire.py::TestExpire::test_check_spin_preservation_wrong_type", "elipy2/tests/test_expire.py::TestExpire::test_cleanup_empty_directories_dry_run", "elipy2/tests/test_expire.py::TestExpire::test_cleanup_empty_directories_exception", "elipy2/tests/test_expire.py::TestExpire::test_cleanup_empty_directories_real_run", "elipy2/tests/test_expire.py::TestExpire::test_delete_all_matching_builds_from_bilbo_no_builds", "elipy2/tests/test_expire.py::TestExpire::test_delete_all_matching_builds_from_bilbo_success", "elipy2/tests/test_expire.py::TestExpire::test_delete_build_from_disk_onefs_api", "elipy2/tests/test_expire.py::TestExpire::test_delete_build_from_disk_robocopy_with_object", "elipy2/tests/test_expire.py::TestExpire::test_delete_build_from_disk_robocopy_without_object", "elipy2/tests/test_expire.py::TestExpire::test_expire", "elipy2/tests/test_expire.py::TestExpire::test_expire_dry_run", "elipy2/tests/test_expire.py::TestExpire::test_expire_handles_exceptions_from_delete_filer_folder", "elipy2/tests/test_expire.py::TestExpire::test_expire_handles_exceptions_from_delete_with_onefs_api", "elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_of_remaining_builds_on_delete_filer_folder_exception", "elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_of_remaining_builds_on_use_onefs_api_exception", "elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception", "elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception", "elipy2/tests/test_expire.py::TestExpire::test_expire_onefs_api", "elipy2/tests/test_expire.py::TestExpire::test_expire_response_dry_run", "elipy2/tests/test_expire.py::TestExpire::test_expire_response_run", "elipy2/tests/test_expire.py::TestExpire::test_expire_response_run_invalid", "elipy2/tests/test_expire.py::TestExpire::test_filter_builds_by_retention_exception", "elipy2/tests/test_expire.py::TestExpire::test_filter_builds_by_retention_no_preserve", "elipy2/tests/test_expire.py::TestExpire::test_filter_builds_by_retention_preserve_build", "elipy2/tests/test_expire.py::TestExpire::test_get_builds_to_expire", "elipy2/tests/test_expire.py::TestExpire::test_get_builds_to_expire_multi", "elipy2/tests/test_expire.py::TestExpire::test_get_preservation_counters_default", "elipy2/tests/test_expire.py::TestExpire::test_get_preservation_counters_exception_handling", "elipy2/tests/test_expire.py::TestExpire::test_get_preservation_counters_with_settings", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_case_insensitive", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_dict_format", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_empty_list_value", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_exception_handling", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_invalid_count_values", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_invalid_string_format", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_list_value", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_mixed_formats", "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_string_format", "elipy2/tests/test_expire.py::TestExpire::test_has_invalid_filename_invalid", "elipy2/tests/test_expire.py::TestExpire::test_has_invalid_filename_valid", "elipy2/tests/test_expire.py::TestExpire::test_is_cl_directory_cl_underscore_patterns", "elipy2/tests/test_expire.py::TestExpire::test_is_cl_directory_invalid_entries", "elipy2/tests/test_expire.py::TestExpire::test_is_cl_directory_valid_cl_numbers", "elipy2/tests/test_expire.py::TestExpire::test_is_cl_directory_version_patterns", "elipy2/tests/test_expire.py::TestExpire::test_is_directory_empty_empty", "elipy2/tests/test_expire.py::TestExpire::test_is_directory_empty_exception", "elipy2/tests/test_expire.py::TestExpire::test_is_directory_empty_not_empty", "elipy2/tests/test_expire.py::TestExpire::test_is_safe_to_cleanup_safe_paths", "elipy2/tests/test_expire.py::TestExpire::test_is_safe_to_cleanup_unsafe_paths", "elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path", "elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path_dry_run", "elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path_keep_all", "elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path_with_onfs_api", "elipy2/tests/test_expire.py::TestExpire::test_no_deletion_needed_false", "elipy2/tests/test_expire.py::TestExpire::test_no_deletion_needed_true", "elipy2/tests/test_expire.py::TestExpire::test_orphan_deletion_sufficient_false", "elipy2/tests/test_expire.py::TestExpire::test_orphan_deletion_sufficient_true", "elipy2/tests/test_expire.py::TestExpire::test_process_build_deletions_dry_run", "elipy2/tests/test_expire.py::TestExpire::test_process_build_deletions_failure", "elipy2/tests/test_expire.py::TestExpire::test_process_build_deletions_invalid_filename", "elipy2/tests/test_expire.py::TestExpire::test_process_build_deletions_success", "elipy2/tests/test_expire.py::TestExpire::test_process_orphaned_builds_deleted_in_bilbo", "elipy2/tests/test_expire.py::TestExpire::test_process_orphaned_builds_old_orphan", "elipy2/tests/test_expire.py::TestExpire::test_process_orphaned_builds_recent_orphan", "elipy2/tests/test_expire.py::TestExpire::test_process_orphaned_builds_valid_in_bilbo", "elipy2/tests/test_expire.py::TestExpire::test_query_all_bilbo_indices_for_build_primary_only", "elipy2/tests/test_expire.py::TestExpire::test_query_all_bilbo_indices_for_build_with_bilbo_v2", "elipy2/tests/test_expire.py::TestExpire::test_query_bilbo_for_build_exception", "elipy2/tests/test_expire.py::TestExpire::test_query_bilbo_for_build_no_results", "elipy2/tests/test_expire.py::TestExpire::test_query_bilbo_for_build_success", "elipy2/tests/test_expire.py::TestExpire::test_safe_execute_decorator_exception", "elipy2/tests/test_expire.py::TestExpire::test_safe_execute_decorator_no_logging", "elipy2/tests/test_expire.py::TestExpire::test_safe_execute_decorator_success", "elipy2/tests/test_expire.py::TestExpire::test_save_tagged_builds", "elipy2/tests/test_expire.py::TestExpire::test_scan_frosty_branch_builds_exception", "elipy2/tests/test_expire.py::TestExpire::test_scan_frosty_branch_builds_normal", "elipy2/tests/test_expire.py::TestExpire::test_scan_frosty_builds_fixed_depth_branch_path", "elipy2/tests/test_expire.py::TestExpire::test_scan_frosty_builds_fixed_depth_no_path", "elipy2/tests/test_expire.py::TestExpire::test_scan_webexport_builds_fixed_depth_no_path", "elipy2/tests/test_expire.py::TestExpire::test_scan_webexport_builds_fixed_depth_normal", "elipy2/tests/test_expire.py::TestExpire::test_should_preserve_build_with_retention_no_builds", "elipy2/tests/test_expire.py::TestExpire::test_should_preserve_build_with_retention_spin_build", "elipy2/tests/test_expire.py::TestExpire::test_sort_builds", "elipy2/tests/test_expire.py::TestExpire::test_sort_builds_by_cl_mixed_paths", "elipy2/tests/test_expire.py::TestExpire::test_sort_builds_by_cl_valid_paths", "elipy2/tests/test_expire.py::TestExpire::test_sort_order_builds", "elipy2/tests/test_fbcli.py::TestFbcli::test_append_fbcli_arg_condition_False", "elipy2/tests/test_fbcli.py::TestFbcli::test_append_fbcli_arg_condition_None_Value", "elipy2/tests/test_fbcli.py::TestFbcli::test_append_fbcli_arg_condition_True", "elipy2/tests/test_fbcli.py::TestFbcli::test_buildsln", "elipy2/tests/test_fbcli.py::TestFbcli::test_buildsln_msbuild_args", "elipy2/tests/test_fbcli.py::TestFbcli::test_cook", "elipy2/tests/test_fbcli.py::TestFbcli::test_cook_multiple_assets", "elipy2/tests/test_fbcli.py::TestFbcli::test_disable_licensee", "elipy2/tests/test_fbcli.py::TestFbcli::test_enable_licensee", "elipy2/tests/test_fbcli.py::TestFbcli::test_gensln", "elipy2/tests/test_fbcli.py::TestFbcli::test_gensln_framework_args", "elipy2/tests/test_fbcli.py::TestFbcli::test_gensln_wsl", "elipy2/tests/test_fbcli.py::TestFbcli::test_icepick_cook", "elipy2/tests/test_fbcli.py::TestFbcli::test_icepick_cook_multiple_assets", "elipy2/tests/test_fbcli.py::TestFbcli::test_install_prerequisites_custom", "elipy2/tests/test_fbcli.py::TestFbcli::test_install_prerequisites_default", "elipy2/tests/test_fbcli.py::TestFbcli::test_nant", "elipy2/tests/test_fbcli.py::TestFbcli::test_pullbuild", "elipy2/tests/test_fbcli.py::TestFbcli::test_pullbuild_false_default_args", "elipy2/tests/test_fbcli.py::TestFbcli::test_pullbuild_path_subpath", "elipy2/tests/test_fbcli.py::TestFbcli::test_pushbuild", "elipy2/tests/test_fbcli.py::TestFbcli::test_pushbuild_eacopy_args", "elipy2/tests/test_fbcli.py::TestFbcli::test_pushbuild_invalid_args", "elipy2/tests/test_fbcli.py::TestFbcli::test_pushbuild_path_subpath_multiple_platforms", "elipy2/tests/test_fbcli.py::TestFbcli::test_run", "elipy2/tests/test_fbcli.py::TestFbcli::test_run_no_method", "elipy2/tests/test_fbcli.py::TestFbcli::test_run_no_method_args", "elipy2/tests/test_fbcli.py::TestFbcli::test_set_licensee_undefined_action", "elipy2/tests/test_fbcli.py::TestFbcli::test_switch_licensee", "elipy2/tests/test_fbcli.py::TestFbcli::test_view_licensee", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_bespoke_frosty", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_bespoke_frosty_args_to_remove", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_bespoke_frosty_region", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbcli", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbenv", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbenv_fail_on_first", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbenv_framework_args", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_buildsln_fbenv_not_fail_on_first", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_all_params", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_all_params_new_fb", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_assets_pipelineargs_fail", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_attach", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_all_params", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_attach", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_not_local", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_only_pipelineargs", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_only_platforms", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_platforms_assets", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbcli_platforms_pipelineargs", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_fbenv_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_index", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_index_deprecated", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_not_local", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_only_assets_fail", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_only_pipelineargs", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_only_platforms", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_platforms_assets", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_platforms_pipelineargs", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_cook_without_params_fail", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_fbcli_gensln_with_stressbulkbuild_arg", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_fbcli_gensln_without_stressbulkbuild_arg", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_fbenv_gensln_with_stressbulkbuild_arg", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_fbenv_gensln_without_stressbulkbuild_arg", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_frosty_all_params_no_bespoke", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_frosty_failure", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_frosty_only_platforms", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_frosty_with_bespoke_flag", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli_with_alltests_and_minimum_fb_version", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli_with_stressbulkbuild_arg", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli_with_wsl_and_not_minimum_fb_version", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbcli_without_stressbulkbuild_arg", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv_variants", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv_with_alltests", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_gensln_fbenv_with_wsl", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_enabled_licensees_fbenv", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_enabled_licensees_nant", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_exe_name_2022", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_exe_name_2022_failure", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_exe_name_pre_2022", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_exe_name_pre_2022_failure", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_before_2022", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_early_2022", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_late_2022_no_utility_file", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_late_2022_no_utility_file_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_late_2022_with_utility_file", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_fbenv_platform_module_late_2022_with_utility_file_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_frosted_directory_api", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_frosted_directory_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_frosted_directory_internal", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_binary_api", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_binary_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_binary_internal", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_directory_api", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_directory_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_game_directory_internal", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_pipeline_directory_api", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_pipeline_directory_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_pipeline_directory_internal", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_platform_data", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_get_platform_data_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_icepick_cook_fbcli", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_icepick_cook_fbenv_settings_not_found", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_initialize", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_install_prerequisites_custom", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_install_prerequisites_default", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_install_prerequisites_fbcli", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_is_api_function_failed_exception_false", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_is_api_function_failed_exception_true", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_normalize_platform", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_normalize_platform_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_failure_fbenv", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_failure_no_input_file", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_failure_no_platform", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_framework_args", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_framework_args_list_of_platforms", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pkgprebuilds_framework_args_tuple_of_platforms", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_default", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_mirror_skip", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_mirror_use", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_not_use_local", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_default", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_extra_args", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_mirror_skip", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_mirror_use", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_not_ignore_lock", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_fbcli_not_use_local", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pullfrostbitebuild_use_local", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_default", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_mirror_skip", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_mirror_use", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_not_use_local", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_fbcli_default", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_fbcli_extra_args", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_fbcli_mirror_skip", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_fbcli_mirror_use", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_pushfrostbitebuild_use_local", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_datadir", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_datadir_fbcli", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_environment_values_api", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_environment_values_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_local_root", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_set_local_root_exception", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_utility_path_exists_false", "elipy2/tests/test_fbenv_layer.py::TestFbenvLayer::test_utility_path_exists_true", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_bad_format_found_in_ess", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_cred_from_ess", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_exception", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_no_cred", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_no_creds_found_in_ess", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_no_drive", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_undefined_network", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_variable", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_vault", "elipy2/tests/test_filer.py::TestFiler::test_auth_network_connection_wrong_creds_keys_in_ess", "elipy2/tests/test_filer.py::TestFiler::test_baseline_ps4_package", "elipy2/tests/test_filer.py::TestFiler::test_baseline_ps5_package", "elipy2/tests/test_filer.py::TestFiler::test_baseline_ps5_package_combine", "elipy2/tests/test_filer.py::TestFiler::test_baseline_ps_package", "elipy2/tests/test_filer.py::TestFiler::test_baseline_xb1_layout", "elipy2/tests/test_filer.py::TestFiler::test_baseline_xb1_layout_no_dest_exception", "elipy2/tests/test_filer.py::TestFiler::test_cancel_network_connection_all", "elipy2/tests/test_filer.py::TestFiler::test_cancel_network_connection_with_argument", "elipy2/tests/test_filer.py::TestFiler::test_create_combine_stream_info_file", "elipy2/tests/test_filer.py::TestFiler::test_create_combine_stream_info_file_deployed", "elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build", "elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build_exception", "elipy2/tests/test_filer.py::TestFiler::test_deploy_ant_local_build_no_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output", "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output_deployed", "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_combine_output_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_state", "elipy2/tests/test_filer.py::TestFiler::test_deploy_avalanche_state_no_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_already_deployed", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_tool", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_tool_deploy_frostedtests", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_artifact_fb2019_with_tnt", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_custom_binaries_destination", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_custom_source", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_deploy_tests_file_issue", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_file_issue", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args_overwrite_check", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_fbenv_args_with_tests", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_tnt", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_fb2019_with_tnt_use_fbenv_copy", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_local", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_mirror_false", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_no_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_not_use_fbcli_custom_tag", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_platform_tool_targets_override", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_skip_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_tool", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_tool_tool_targets_override", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_artifact_override", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_custom_tag", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_multi_licensee", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_no_licensee", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_use_fbcli_single_licensee", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_with_tnt", "elipy2/tests/test_filer.py::TestFiler::test_deploy_code_with_tnt_overwrite", "elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles", "elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles_deployed", "elipy2/tests/test_filer.py::TestFiler::test_deploy_delta_bundles_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build", "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_combined", "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_custom_source", "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_dest_exists_exception", "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_no_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_no_source_specified", "elipy2/tests/test_filer.py::TestFiler::test_deploy_frosty_build_with_combine_params", "elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles", "elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles_deployed", "elipy2/tests/test_filer.py::TestFiler::test_deploy_head_bundles_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_deploy_state", "elipy2/tests/test_filer.py::TestFiler::test_deploy_state_deployed", "elipy2/tests/test_filer.py::TestFiler::test_deploy_state_non_default_bundle_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build", "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_failure", "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_nomaster", "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_zip", "elipy2/tests/test_filer.py::TestFiler::test_deploy_tnt_local_build_zip_failure", "elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state", "elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_no_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_no_dir", "elipy2/tests/test_filer.py::TestFiler::test_fetch_avalanche_state_use_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_dest", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_dest_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_no_dest", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_no_dest_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_head_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_bundles_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state_no_dest", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_state_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest_combine", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_win64_chunkmanifest_failure_no_destination", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_wrong_platform", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_new", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_no_dest_exception", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xb1_old", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx_combine", "elipy2/tests/test_filer.py::TestFiler::test_fetch_baseline_xb_priorpackage_xbsx_exception_file_not_found", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_artifact_fb2019_no_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_custom_destination", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo_fbenv_args", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_fb2019_no_bilbo_fetch_tests", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_no_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_not_use_bilbo", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_not_use_fbcli_custom_tag", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_tool", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_artifact_override", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_custom_tag", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_multi_licensee", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_no_licensee", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_use_fbcli_single_licensee", "elipy2/tests/test_filer.py::TestFiler::test_fetch_code_with_unknown_platform", "elipy2/tests/test_filer.py::TestFiler::test_fetch_delta_bundles", "elipy2/tests/test_filer.py::TestFiler::test_fetch_delta_bundles_no_dest", "elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build", "elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_by_source", "elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_by_source_dest", "elipy2/tests/test_filer.py::TestFiler::test_fetch_frosty_build_dest", "elipy2/tests/test_filer.py::TestFiler::test_fetch_head_bundles", "elipy2/tests/test_filer.py::TestFiler::test_fetch_head_bundles_non_default_dir_name", "elipy2/tests/test_filer.py::TestFiler::test_get_case_sensitive_path_failed", "elipy2/tests/test_filer.py::TestFiler::test_get_case_sensitive_path_success", "elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build", "elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_no_dir", "elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_plt", "elipy2/tests/test_filer.py::TestFiler::test_import_ant_local_build_plt_no_cl", "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build", "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_http_error", "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_not_there", "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt", "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_no_cl", "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_nobilbo", "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_plt_nomaster", "elipy2/tests/test_filer.py::TestFiler::test_import_tnt_local_build_zip", "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_failed_deletion", "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_failed_validation", "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_key_error", "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_on_success", "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_path_doesnt_exist", "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_path_exist_but_is_broken", "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_usrpass", "elipy2/tests/test_filer.py::TestFiler::test_onefs_api_delete_usrpass_none_found", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_add_build_share_user_postfix_noautobuild", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_add_build_share_user_postfix_noautobuild_no_user", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_ant_local_build_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_avalanche_export_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_build_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_build_path_non_default_location", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_delta_bundles_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_delta_bundles_path_non_default_bundles_dir_name", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_head_bundles_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_head_bundles_path_non_default_bundles_dir_name", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_state_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_baseline_state_path_non_default_dir_name", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_binlog_build_path_meta", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_binlog_build_path_non_meta", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_build_share_path_buildsystem", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_build_share_path_no_user", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_build_share_path_user", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_path_custom_tag", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_path_with_target_build_share[game-dev-1337-plt-final-None-None-expected0]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_path_with_target_build_share[game-dev-1337-plt-final-None-alt_buildshare-expected1]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_platform_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_root_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_root_path_custom_tag", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_root_path_nomaster", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_build_root_path_stressbulkbuild", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_platform_path_with_target_build_share[game-dev-1337-plt-final-None-None]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_platform_path_with_target_build_share[game-dev-1337-plt-final-None-alt_buildshare]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_platform_path_with_target_build_share[game-dev-1337-plt-final-criterion-None]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_code_platform_path_with_target_build_share[game-dev-1337-plt-final-dice-alt_buildshare]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_delta_bundles_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_delta_bundles_path_non_default_bundles_dir_name", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_expression_debug_data_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_frosty_base_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_frosty_build_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_frosty_build_path_combine", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_frosty_build_path_with_content_layer", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_head_bundles_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_head_bundles_path_non_default_bundles_dir_name", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_basic_build", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_basic_build_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_basic_drone", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_basic_drone_build_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_offsite_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_outsourcer_build", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_outsourcer_build_location", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_shift_delivery_path_code", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_shift_delivery_path_other_build_type", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_shift_processing_target_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_state_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_symbol_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_symbol_path_win64", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_tnt_local_path", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_tnt_local_path_nomaster", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_get_tnt_local_path_stressbulkbuild", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[None-bfglacier-default location alternate build share bfglacier]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[criterion-None-criterion location build share]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[criterion-nfsexcalibur-criterion location alternate build share nfsexcalibur]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[criterion-nfsmerlin-criterion location alternate build share nfsmerlin]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[default-bfglacier-default location alternate build share bfglacier]", "elipy2/tests/test_filer_paths.py::TestFilerPaths::test_settings_get_with_custom_build_shares_and_locations[default-mirrorsedge-default location alternate build share mirrorsedge]", "elipy2/tests/test_frostbite_build_agent_utils.py::TestFrostbiteBuildAgentUtils::test_generic_threshold_clean_called_with_all_arguments", "elipy2/tests/test_frostbite_build_agent_utils.py::TestFrostbiteBuildAgentUtils::test_generic_threshold_clean_called_with_correct_arguments", "elipy2/tests/test_frostbite_build_agent_utils.py::TestFrostbiteBuildAgentUtils::test_generic_threshold_clean_cleaup_not_called_when_invalid_workspace", "elipy2/tests/test_frostbite_build_agent_utils.py::TestFrostbiteBuildAgentUtils::test_get_cleanup_script_path", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_branch_id", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_game_data_dir", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_game_root", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_licensee_code_folder", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_licensee_id", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_get_tnt_root", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_minimum_fb_version", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_minimum_fb_version_except", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_read_fb_version", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_set_monkey_build_label", "elipy2/tests/test_frostbite_core.py::TestCoreModule::test_set_monkey_build_label_default", "elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_branch_id_exception", "elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_game_data_dir_exception", "elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_game_root_exception", "elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_licensee_id_exception", "elipy2/tests/test_frostbite_core.py::TestCoreModuleExceptions::test_get_tnt_root_exception", "elipy2/tests/test_icepick.py::TestIcepick::test_clean_icepicktemp", "elipy2/tests/test_icepick.py::TestIcepick::test_clean_local_frosty", "elipy2/tests/test_icepick.py::TestIcepick::test_format_settings_files", "elipy2/tests/test_icepick.py::TestIcepick::test_get_extra_test_suite_args_basic", "elipy2/tests/test_icepick.py::TestIcepick::test_get_extra_test_suite_args_custom_test_suite_data", "elipy2/tests/test_icepick.py::TestIcepick::test_get_extra_test_suite_args_test_group", "elipy2/tests/test_icepick.py::TestIcepick::test_get_fb_icepick_args_all", "elipy2/tests/test_icepick.py::TestIcepick::test_get_fb_icepick_args_basic", "elipy2/tests/test_icepick.py::TestIcepick::test_minimum_args", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_exception", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_fbcli", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_fbcli_default_extra_framework_args", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_hailstorm", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_hailstorm_disabled", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_ignore_exception", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_override_settings_empty", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_cook_raise_exception", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_fail", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_fbcli", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_fbcli_args", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_fbcli_args_elipy_config_args", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_ps4", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_ps5", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_test_suite", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_with_config", "elipy2/tests/test_icepick.py::TestIcepick::test_run_icepick_with_exception", "elipy2/tests/test_init.py::TestBeforeSend::test_fbenv_event_dropped", "elipy2/tests/test_init.py::TestBeforeSend::test_normal_event", "elipy2/tests/test_init.py::TestDropUnwantedEvents::test_drop_unwanted_events", "elipy2/tests/test_init.py::TestDropUnwantedEvents::test_drop_unwanted_events_drop_event", "elipy2/tests/test_init.py::TestIsDryRun::test_is_dry_run", "elipy2/tests/test_init.py::TestLogInstallationSource::test_log_installation_source_af1_no_token", "elipy2/tests/test_init.py::TestLogInstallationSource::test_log_installation_source_af1_no_user", "elipy2/tests/test_init.py::TestLogInstallationSource::test_log_installation_source_af1_none_set", "elipy2/tests/test_init.py::TestLogInstallationSource::test_log_installation_source_af2", "elipy2/tests/test_init.py::TestLogging::test_str_to_log_level", "elipy2/tests/test_init.py::TestSentrySetup::test_ado_tags", "elipy2/tests/test_init.py::TestSentrySetup::test_enable_variable[os_env_patch0]", "elipy2/tests/test_init.py::TestSentrySetup::test_enable_variable[os_env_patch1]", "elipy2/tests/test_init.py::TestSentrySetup::test_frostbite_variables[os_env_patch0]", "elipy2/tests/test_init.py::TestSentrySetup::test_frostbite_variables[os_env_patch1]", "elipy2/tests/test_init.py::TestSentrySetup::test_jenkins_tags", "elipy2/tests/test_init.py::TestSetCodeArea::test_fbenv_stacktrace", "elipy2/tests/test_init.py::TestSetCodeArea::test_handles_exceptions", "elipy2/tests/test_init.py::TestSetCodeArea::test_set_code_area", "elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_get_enabled_licensee_names_called", "elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_licensee_update_enables_called", "elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_nant_generate_licensee_fragment_info_called", "elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_nant_get_enabled_licensee_names_called", "elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_set_licensee_code_config_called", "elipy2/tests/test_licensee_utils.py::TestLicenseeUtils::test_set_nant_cached_licensee_dict", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_ant_local_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_ant_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_ant_path_fail", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_antifreeze_dir", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_avalanchecli_exe_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_avalanchecli_exe_path_exception", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_dataset_state_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_executable_name_pattern", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_fdu_dir", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_game_binary_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_game_binary_path_fbenv", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_game_binary_path_not_defined", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_game_drive_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_artifact_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_avalanche_export_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_ant", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_frosted", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_general", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_pipeline", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_fbenv_tool", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_build_path_non_fbenv", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_bundles_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_bundles_path_non_default", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_expressiondebug_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_frosty_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_licensee_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_local_scripts_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_logs_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_packages_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_packagesdev_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_pip_cache_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_dll", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_no_platform", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_non_win64", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_non_win64_no_config", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_tool_x3", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_win64", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_win64_no_config", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_fbenv_win64_trial", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_no_config", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_platform_path_unknown_platform", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_tnt_localpackages_path", "elipy2/tests/test_local_paths.py::TestLocalPaths::test_get_tnt_packages_path", "elipy2/tests/test_multiprocessing.py::TestCoreModule::test_run_map", "elipy2/tests/test_multiprocessing.py::TestCoreModule::test_run_starmap", "elipy2/tests/test_multiprocessing.py::test_func", "elipy2/tests/test_oreans.py::TestOreans::test__get_oreans_program_args", "elipy2/tests/test_oreans.py::TestOreans::test_fetch_oreans_files", "elipy2/tests/test_oreans.py::TestOreans::test_protect", "elipy2/tests/test_p4.py::TestP4::test__p4", "elipy2/tests/test_p4.py::TestP4::test__p4_return_value", "elipy2/tests/test_p4.py::TestP4::test__switch", "elipy2/tests/test_p4.py::TestP4::test_change_dont_discard", "elipy2/tests/test_p4.py::TestP4::test_change_full", "elipy2/tests/test_p4.py::TestP4::test_changes_range_keep_start", "elipy2/tests/test_p4.py::TestP4::test_changes_range_start_not_in_range", "elipy2/tests/test_p4.py::TestP4::test_changes_range_with_end", "elipy2/tests/test_p4.py::TestP4::test_changes_range_with_head", "elipy2/tests/test_p4.py::TestP4::test_check_for_tags", "elipy2/tests/test_p4.py::TestP4::test_check_for_tags_byte_input", "elipy2/tests/test_p4.py::TestP4::test_check_for_tags_empty_p4_response", "elipy2/tests/test_p4.py::TestP4::test_check_for_tags_false", "elipy2/tests/test_p4.py::TestP4::test_check_for_tags_missing_tags", "elipy2/tests/test_p4.py::TestP4::test_check_for_tags_multiple_changelists", "elipy2/tests/test_p4.py::TestP4::test_check_for_tags_no_match", "elipy2/tests/test_p4.py::TestP4::test_clean", "elipy2/tests/test_p4.py::TestP4::test_clean_folder", "elipy2/tests/test_p4.py::TestP4::test_clean_no_data", "elipy2/tests/test_p4.py::TestP4::test_clean_no_keys", "elipy2/tests/test_p4.py::TestP4::test_clean_perforce", "elipy2/tests/test_p4.py::TestP4::test_clean_quiet", "elipy2/tests/test_p4.py::TestP4::test_copy", "elipy2/tests/test_p4.py::TestP4::test_copy_args", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_args", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_data_exec", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_data_not_merges", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_data_valid", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_force_copy_non_reverse", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_force_copy_reverse", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_must_sync", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_nodata", "elipy2/tests/test_p4.py::TestP4::test_copy_mapping_non_reverse_default_force", "elipy2/tests/test_p4.py::TestP4::test_delete_workspace", "elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_fetch", "elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_generic_failure", "elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_no_code", "elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_non_failing_error", "elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_non_failing_error_quiet", "elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_push", "elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_quiet", "elipy2/tests/test_p4.py::TestP4::test_dvcs_errors_remote_spec_failure", "elipy2/tests/test_p4.py::TestP4::test_edit", "elipy2/tests/test_p4.py::TestP4::test_edit_failure", "elipy2/tests/test_p4.py::TestP4::test_edit_integrated_files", "elipy2/tests/test_p4.py::TestP4::test_edit_integrated_files_none_found", "elipy2/tests/test_p4.py::TestP4::test_fetch_basic", "elipy2/tests/test_p4.py::TestP4::test_fetch_dry_run", "elipy2/tests/test_p4.py::TestP4::test_fetch_dvcs_errors", "elipy2/tests/test_p4.py::TestP4::test_fetch_extra_args", "elipy2/tests/test_p4.py::TestP4::test_fetch_quiet", "elipy2/tests/test_p4.py::TestP4::test_fetch_verbose", "elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags", "elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags_empty_p4_response", "elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags_false", "elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags_missing_tags", "elipy2/tests/test_p4.py::TestP4::test_get_cl_by_tags_multiple_changelists", "elipy2/tests/test_p4.py::TestP4::test_get_description_cl", "elipy2/tests/test_p4.py::TestP4::test_get_description_empty_p4_response", "elipy2/tests/test_p4.py::TestP4::test_get_description_missing_desc_and_data", "elipy2/tests/test_p4.py::TestP4::test_get_description_multiple_cl", "elipy2/tests/test_p4.py::TestP4::test_get_description_no_access", "elipy2/tests/test_p4.py::TestP4::test_get_description_no_access_2", "elipy2/tests/test_p4.py::TestP4::test_get_description_no_cl", "elipy2/tests/test_p4.py::TestP4::test_get_description_no_valid", "elipy2/tests/test_p4.py::TestP4::test_get_description_other_data", "elipy2/tests/test_p4.py::TestP4::test_getcounter", "elipy2/tests/test_p4.py::TestP4::test_getcounter_none_found", "elipy2/tests/test_p4.py::TestP4::test_ignores", "elipy2/tests/test_p4.py::TestP4::test_integrate", "elipy2/tests/test_p4.py::TestP4::test_integrate_bad_resolve_mode", "elipy2/tests/test_p4.py::TestP4::test_integrate_datavalid", "elipy2/tests/test_p4.py::TestP4::test_integrate_exception_invalid_option", "elipy2/tests/test_p4.py::TestP4::test_integrate_exception_not_in_client_view", "elipy2/tests/test_p4.py::TestP4::test_integrate_exception_usage", "elipy2/tests/test_p4.py::TestP4::test_integrate_exception_wrong_arguments", "elipy2/tests/test_p4.py::TestP4::test_integrate_exe", "elipy2/tests/test_p4.py::TestP4::test_integrate_exe_too_many_rows_scanned", "elipy2/tests/test_p4.py::TestP4::test_integrate_ignore_source_history", "elipy2/tests/test_p4.py::TestP4::test_integrate_no_data", "elipy2/tests/test_p4.py::TestP4::test_integrate_no_mapping", "elipy2/tests/test_p4.py::TestP4::test_integrate_resolve_mode_lst", "elipy2/tests/test_p4.py::TestP4::test_integrate_resolve_mode_str", "elipy2/tests/test_p4.py::TestP4::test_integrate_reverse", "elipy2/tests/test_p4.py::TestP4::test_integrate_stream_with_parent", "elipy2/tests/test_p4.py::TestP4::test_integrate_use_file_paths", "elipy2/tests/test_p4.py::TestP4::test_integrate_use_file_paths_with_revision", "elipy2/tests/test_p4.py::TestP4::test_interchanges_cl", "elipy2/tests/test_p4.py::TestP4::test_interchanges_no_change", "elipy2/tests/test_p4.py::TestP4::test_interchanges_no_valid", "elipy2/tests/test_p4.py::TestP4::test_merge", "elipy2/tests/test_p4.py::TestP4::test_merge_both_data_correct", "elipy2/tests/test_p4.py::TestP4::test_merge_both_exe", "elipy2/tests/test_p4.py::TestP4::test_merge_no_data", "elipy2/tests/test_p4.py::TestP4::test_merge_reverse", "elipy2/tests/test_p4.py::TestP4::test_opened", "elipy2/tests/test_p4.py::TestP4::test_opened_empty_response", "elipy2/tests/test_p4.py::TestP4::test_opened_unexpected_response", "elipy2/tests/test_p4.py::TestP4::test_p4set", "elipy2/tests/test_p4.py::TestP4::test_push_basic", "elipy2/tests/test_p4.py::TestP4::test_push_dry_run", "elipy2/tests/test_p4.py::TestP4::test_push_dvcs_errors", "elipy2/tests/test_p4.py::TestP4::test_push_extra_args", "elipy2/tests/test_p4.py::TestP4::test_push_quiet", "elipy2/tests/test_p4.py::TestP4::test_push_verbose", "elipy2/tests/test_p4.py::TestP4::test_reconcile_fail", "elipy2/tests/test_p4.py::TestP4::test_reconcile_post_result", "elipy2/tests/test_p4.py::TestP4::test_reconcile_quiet", "elipy2/tests/test_p4.py::TestP4::test_reconcile_success1", "elipy2/tests/test_p4.py::TestP4::test_reconcile_success2", "elipy2/tests/test_p4.py::TestP4::test_remotes_log", "elipy2/tests/test_p4.py::TestP4::test_remotes_no_data_returned", "elipy2/tests/test_p4.py::TestP4::test_remotes_not_force_case_insensitive", "elipy2/tests/test_p4.py::TestP4::test_remotes_quiet", "elipy2/tests/test_p4.py::TestP4::test_remotes_with_data", "elipy2/tests/test_p4.py::TestP4::test_reopen", "elipy2/tests/test_p4.py::TestP4::test_reshelve", "elipy2/tests/test_p4.py::TestP4::test_reshelve_no_pending_changelist", "elipy2/tests/test_p4.py::TestP4::test_reshelve_pending_changelist", "elipy2/tests/test_p4.py::TestP4::test_reshelve_with_all", "elipy2/tests/test_p4.py::TestP4::test_reshelve_with_target", "elipy2/tests/test_p4.py::TestP4::test_resolve", "elipy2/tests/test_p4.py::TestP4::test_resolve_failed", "elipy2/tests/test_p4.py::TestP4::test_resolve_failed2", "elipy2/tests/test_p4.py::TestP4::test_resolve_mode", "elipy2/tests/test_p4.py::TestP4::test_resolve_mode_invalid", "elipy2/tests/test_p4.py::TestP4::test_resolve_nodata", "elipy2/tests/test_p4.py::TestP4::test_resolve_one", "elipy2/tests/test_p4.py::TestP4::test_resolve_path", "elipy2/tests/test_p4.py::TestP4::test_revert", "elipy2/tests/test_p4.py::TestP4::test_revert_no_data_in_return", "elipy2/tests/test_p4.py::TestP4::test_revert_quiet", "elipy2/tests/test_p4.py::TestP4::test_revert_return_data", "elipy2/tests/test_p4.py::TestP4::test_revert_with_wipe_and_only_changed_true", "elipy2/tests/test_p4.py::TestP4::test_set_description", "elipy2/tests/test_p4.py::TestP4::test_set_description_code_error", "elipy2/tests/test_p4.py::TestP4::test_set_description_code_info", "elipy2/tests/test_p4.py::TestP4::test_set_description_code_stat", "elipy2/tests/test_p4.py::TestP4::test_set_description_no_code", "elipy2/tests/test_p4.py::TestP4::test_set_description_no_description_returned", "elipy2/tests/test_p4.py::TestP4::test_set_environment", "elipy2/tests/test_p4.py::TestP4::test_set_environment_no_values", "elipy2/tests/test_p4.py::TestP4::test_setcounter", "elipy2/tests/test_p4.py::TestP4::test_setcounter_no_value", "elipy2/tests/test_p4.py::TestP4::test_setcounter_wrong_return_value", "elipy2/tests/test_p4.py::TestP4::test_shelve_dont_discard", "elipy2/tests/test_p4.py::TestP4::test_shelve_dont_force", "elipy2/tests/test_p4.py::TestP4::test_shelve_full", "elipy2/tests/test_p4.py::TestP4::test_stream_info", "elipy2/tests/test_p4.py::TestP4::test_stream_info_doesnt_exist", "elipy2/tests/test_p4.py::TestP4::test_submit", "elipy2/tests/test_p4.py::TestP4::test_submit_call", "elipy2/tests/test_p4.py::TestP4::test_submit_call_revert_unchanged", "elipy2/tests/test_p4.py::TestP4::test_submit_empty", "elipy2/tests/test_p4.py::TestP4::test_submit_exception", "elipy2/tests/test_p4.py::TestP4::test_submit_nofiles", "elipy2/tests/test_p4.py::TestP4::test_submit_nofiles_v2", "elipy2/tests/test_p4.py::TestP4::test_switch", "elipy2/tests/test_p4.py::TestP4::test_sync", "elipy2/tests/test_p4.py::TestP4::test_sync_error_failure", "elipy2/tests/test_p4.py::TestP4::test_sync_error_ignore", "elipy2/tests/test_p4.py::TestP4::test_sync_to_revision", "elipy2/tests/test_p4.py::TestP4::test_sync_write_to_logfile", "elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files", "elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files_no_resolve_type_for_file", "elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files_no_resolve_type_in_input", "elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files_other_resolve_type", "elipy2/tests/test_p4.py::TestP4::test_unresolved_with_files_some_missing_local", "elipy2/tests/test_p4.py::TestP4::test_unresolved_without_files", "elipy2/tests/test_p4.py::TestP4::test_unresolved_without_files_error_data_missing", "elipy2/tests/test_p4.py::TestP4::test_unshelve", "elipy2/tests/test_p4.py::TestP4::test_unshelve_code_missing_in_response_item", "elipy2/tests/test_p4.py::TestP4::test_unshelve_exception", "elipy2/tests/test_p4.py::TestP4::test_unshelve_no_force", "elipy2/tests/test_p4.py::TestP4::test_unshelve_none", "elipy2/tests/test_p4.py::TestP4::test_unshelve_override_default_cl", "elipy2/tests/test_p4.py::TestP4::test_unshelve_success", "elipy2/tests/test_p4.py::TestP4::test_wipe_client", "elipy2/tests/test_p4.py::TestP4Other::test_describe", "elipy2/tests/test_p4.py::TestP4Other::test_describe_no_changelist", "elipy2/tests/test_p4.py::TestP4Other::test_describe_shelved", "elipy2/tests/test_p4.py::TestP4Other::test_describe_show_shelved_files", "elipy2/tests/test_p4.py::TestP4Other::test_get_client", "elipy2/tests/test_p4.py::TestP4Other::test_p4_executable_does_not_exist", "elipy2/tests/test_p4.py::TestP4Other::test_p4_executable_exists", "elipy2/tests/test_p4.py::TestP4Other::test_p4_filenotfounderror", "elipy2/tests/test_p4.py::TestP4Other::test_p4_valueerror", "elipy2/tests/test_package.py::TestPackage::test__fetch_version", "elipy2/tests/test_package.py::TestPackage::test__supported", "elipy2/tests/test_package.py::TestPackage::test_build_system_init", "elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator", "elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator_exists_xb1", "elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator_exists_xbsx", "elipy2/tests/test_package.py::TestPackage::test_copy_submissionvalidator_xbsx", "elipy2/tests/test_package.py::TestPackage::test_fetch_appversion", "elipy2/tests/test_package.py::TestPackage::test_fetch_appversion_double", "elipy2/tests/test_package.py::TestPackage::test_fetch_appversion_double_digit", "elipy2/tests/test_package.py::TestPackage::test_fetch_appversion_no_files", "elipy2/tests/test_package.py::TestPackage::test_fetch_appversion_no_path", "elipy2/tests/test_package.py::TestPackage::test_fetch_contversion", "elipy2/tests/test_package.py::TestPackage::test_frosty", "elipy2/tests/test_package.py::TestPackage::test_frosty_with_exception", "elipy2/tests/test_package.py::TestPackage::test_frosty_with_keyerror", "elipy2/tests/test_package.py::TestPackage::test_frosty_with_params", "elipy2/tests/test_package.py::TestPackage::test_get_disc_package_type_both", "elipy2/tests/test_package.py::TestPackage::test_get_disc_package_type_digital", "elipy2/tests/test_package.py::TestPackage::test_get_disc_package_type_none", "elipy2/tests/test_package_utils.py::TestPackageUtils::test_find_package_called", "elipy2/tests/test_package_utils.py::TestPackageUtils::test_nant_on_package_called", "elipy2/tests/test_package_utils.py::TestPackageUtils::test_nant_on_package_exception", "elipy2/tests/test_package_utils.py::TestPackageUtils::test_package_downloads_called", "elipy2/tests/test_requirements.py::test_elipy2_requirements_txt_aligned_with_elipy2_setup_py", "elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs0-setup_py_reqs0-expected0]", "elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs1-setup_py_reqs1-expected1]", "elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs2-setup_py_reqs2-expected2]", "elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs3-setup_py_reqs3-expected3]", "elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs4-setup_py_reqs4-expected4]", "elipy2/tests/test_requirements.py::test_rec_parser_compare_sets[requirements_txt_reqs5-setup_py_reqs5-expected5]", "elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock!=5.0.2-expected1]", "elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock<5.0.2-expected5]", "elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock<=5.0.2-expected2]", "elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock==5.0.2-expected0]", "elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock===5.0.2-expected7]", "elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock>5.0.2-expected4]", "elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock>=5.0.2-expected3]", "elipy2/tests/test_requirements.py::test_req_parser_breakdown_requirement[mock~=5.0.2-expected6]", "elipy2/tests/test_retry.py::TestMetadata::test_retry_metadata", "elipy2/tests/test_retry.py::TestMetadata::test_retry_mocking", "elipy2/tests/test_retry.py::test_retry_with_exception", "elipy2/tests/test_retry.py::test_retry_with_no_exception", "elipy2/tests/test_retry.py::test_retry_with_return_value", "elipy2/tests/test_retry.py::test_retry_with_settings_disabled", "elipy2/tests/test_running_processes.py::TestCoreModule::test_kill", "elipy2/tests/test_running_processes.py::TestCoreModule::test_kill_list", "elipy2/tests/test_running_processes.py::TestCoreModule::test_kill_nothing", "elipy2/tests/test_running_processes.py::TestCoreModule::test_kill_nothing_not_a_process", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_authenticate_eapm_credstore_2021", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_authenticate_eapm_credstore_exception", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_authenticate_eapm_credstore_p4", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_authenticate_eapm_credstore_pre2021", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_ps5_tools", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_ps5_tools_include_wfmfd", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_ps5_tools_skip_version_checks", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_check_installed", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_credstore_called", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_credstore_not_called", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_fbenv_with_platform", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_fbenv_without_platform", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_non_ps5_2022", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_non_ps5_pre2022", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_ps5_2022", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_ps5_pre2022", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_nantonpackage_ps5_pre2022_failure", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_ps5_2023", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_ps5_2024", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_install_sdks_elipy_update_shell", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_not_console", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_one_platform", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_shift_build", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_xb1_new", "elipy2/tests/test_sdk_utils.py::TestSdkUtils::test_run_install_required_sdk_xb1_old", "elipy2/tests/test_secrets.py::TestSecrets::test_eapm_credstore_credentialfile", "elipy2/tests/test_secrets.py::TestSecrets::test_eapm_credstore_fails", "elipy2/tests/test_secrets.py::TestSecrets::test_eapm_credstore_runfails", "elipy2/tests/test_secrets.py::TestSecrets::test_eapm_credstore_user_password", "elipy2/tests/test_secrets.py::TestSecrets::test_get_real_secret_output_path", "elipy2/tests/test_secrets.py::TestSecrets::test_get_real_secret_output_path_tnt", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secret", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_already_downloaded", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_requires_downloaded", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_retry[ConnectionError]", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_retry[UnexpectedError]", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secret_to_file", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secrets", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secrets_from_ess", "elipy2/tests/test_secrets.py::TestSecrets::test_get_secrets_none", "elipy2/tests/test_secrets.py::TestSecrets::test_remove_old_frosty_cert", "elipy2/tests/test_secrets.py::TestSecrets::test_secret_credential_file_store", "elipy2/tests/test_secrets.py::TestSecrets::test_secret_credential_file_store_invalid_key", "elipy2/tests/test_secrets.py::TestSecrets::test_secret_getter_connect_approle", "elipy2/tests/test_secrets.py::TestSecrets::test_secret_getter_connect_token", "elipy2/tests/test_secrets.py::TestSecrets::test_secret_getter_get_value", "elipy2/tests/test_secrets.py::TestSecrets::test_secret_getter_get_value_secret_key", "elipy2/tests/test_secrets.py::TestSecrets::test_secret_matches_context", "elipy2/tests/test_setup_metadata_manager.py::TestGetProviderSettings::test_get_provider_settings", "elipy2/tests/test_setup_metadata_manager.py::TestGetProviderSettings::test_get_provider_settings_no_settings", "elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerDefaultLocation::test_default", "elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_class_overrides", "elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_index_overrides_primary", "elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_index_overrides_secondary", "elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_url_overrides_primary", "elipy2/tests/test_setup_metadata_manager.py::TestSetupMetadataManagerOverridesLocation::test_url_overrides_secondary", "elipy2/tests/test_shift_utils.py::TestShift::test_call_determine_build_location_directly", "elipy2/tests/test_shift_utils.py::TestShift::test_call_process_shift_upload_directly", "elipy2/tests/test_shift_utils.py::TestShift::test_create_template_file", "elipy2/tests/test_shift_utils.py::TestShift::test_create_template_file_file_exist", "elipy2/tests/test_shift_utils.py::TestShift::test_create_template_file_without_supplemental", "elipy2/tests/test_shift_utils.py::TestShift::test_download_files", "elipy2/tests/test_shift_utils.py::TestShift::test_find_builds_bilbo", "elipy2/tests/test_shift_utils.py::TestShift::test_find_shift_builds", "elipy2/tests/test_shift_utils.py::TestShift::test_find_shift_builds_no_files", "elipy2/tests/test_shift_utils.py::TestShift::test_find_shift_builds_no_path", "elipy2/tests/test_shift_utils.py::TestShift::test_get_build_status_api_call", "elipy2/tests/test_shift_utils.py::TestShift::test_get_build_status_return", "elipy2/tests/test_shift_utils.py::TestShift::test_get_config_data", "elipy2/tests/test_shift_utils.py::TestShift::test_get_config_path", "elipy2/tests/test_shift_utils.py::TestShift::test_get_latest_build_id_by_sku", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_changelist_label_both", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_changelist_label_code_only", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_parse_branch_code", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_parse_branch_data", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_with_build_and_sku_name_different_code_data_changelists", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_with_build_and_sku_name_same_code_data_changelists", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_without_build_and_sku_name_different_code_data_changelists", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_data_without_build_and_sku_name_same_code_data_changelists", "elipy2/tests/test_shift_utils.py::TestShift::test_get_shift_submission_tool", "elipy2/tests/test_shift_utils.py::TestShift::test_parse_config_data", "elipy2/tests/test_shift_utils.py::TestShift::test_parse_config_data_content_layer", "elipy2/tests/test_shift_utils.py::TestShift::test_parse_config_data_empty", "elipy2/tests/test_shift_utils.py::TestShift::test_parse_config_data_version_and_upload_loop", "elipy2/tests/test_shift_utils.py::TestShift::test_parse_filer_frosty_path", "elipy2/tests/test_shift_utils.py::TestShift::test_parse_filer_frosty_path_with_combine", "elipy2/tests/test_shift_utils.py::TestShift::test_parse_filer_frosty_path_with_content_layer", "elipy2/tests/test_shift_utils.py::TestShift::test_parse_filer_frosty_path_with_content_layer_lowercase", "elipy2/tests/test_shift_utils.py::TestShift::test_read_shift_file", "elipy2/tests/test_shift_utils.py::TestShift::test_read_shift_file2", "elipy2/tests/test_shift_utils.py::TestShift::test_reshift_check", "elipy2/tests/test_shift_utils.py::TestShift::test_reshift_check_false", "elipy2/tests/test_shift_utils.py::TestShift::test_reshift_check_no_bilbo", "elipy2/tests/test_shift_utils.py::TestShift::test_run_upload", "elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_extract_build_id", "elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_extract_build_id_not_found", "elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_logs_error_on_on_failure", "elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_raises_elipyexception_on_non_zero_shift_executable_exit_code", "elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_with_base_build_id", "elipy2/tests/test_shift_utils.py::TestShift::test_run_upload_without_base_build_id", "elipy2/tests/test_shift_utils.py::TestShift::test_set_retention_policy", "elipy2/tests/test_shift_utils.py::TestShift::test_shift_copy", "elipy2/tests/test_shift_utils.py::TestShift::test_shift_copy_no_files", "elipy2/tests/test_shift_utils.py::TestShift::test_shift_register_shift_in_bilbo", "elipy2/tests/test_shift_utils.py::TestShift::test_upload", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_build_set_retention_policy", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_fake_sku", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_no_bilbo", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_patch_remaster", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_reshift", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_single_build_with_incremental_delivery_and_build_id_set", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_single_build_with_incremental_delivery_and_no_build_id", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_single_build_without_incremental_delivery", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_with_elipyexception", "elipy2/tests/test_shift_utils.py::TestShift::test_upload_with_exception", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_fail", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_fail2", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_fail_sku", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_files_present", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_name_too_long", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_remastered", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_sup_file", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_shiftdata_sup_file_retail", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_supplemental_files_present", "elipy2/tests/test_shift_utils.py::TestShift::test_validate_upload_loop_files_present", "elipy2/tests/test_shift_utils.py::TestShift::test_wait_until_build_available", "elipy2/tests/test_shift_utils.py::TestShift::test_wait_until_build_available_no_build_id", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_child_class_init_overwrites_super_kwargs", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_child_class_init_uses_super_defaults", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_child_class_kwarg_is_passed_to_super_init", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_determine_build_location_offsite_drone_non_zipped", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_determine_build_location_offsite_drone_zipped", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_initialization_exception_raised_when_required_param_is_none", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_initialization_uses_super_defaults_when_passed_param_is_set", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_platform_instance_variable_set_to_tools", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_process_shift_upload", "elipy2/tests/test_shifters.py::TestBasicDroneShifter::test_process_shift_upload_failure", "elipy2/tests/test_shifters.py::TestFrostyShifter::test_child_class_init_overwrites_super_kwargs", "elipy2/tests/test_shifters.py::TestFrostyShifter::test_child_class_init_uses_super_defaults", "elipy2/tests/test_shifters.py::TestFrostyShifter::test_child_class_kwarg_is_passed_to_super_init", "elipy2/tests/test_shifters.py::TestFrostyShifter::test_determine_build_location", "elipy2/tests/test_shifters.py::TestFrostyShifter::test_initialization_exception_raised_when_required_param_is_none", "elipy2/tests/test_shifters.py::TestFrostyShifter::test_initialization_uses_super_defaults_when_passed_param_is_set", "elipy2/tests/test_shifters.py::TestFrostyShifter::test_process_shift_upload", "elipy2/tests/test_shifters.py::TestFrostyShifter::test_process_shift_upload_failure", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_child_class_init_overwrites_super_kwargs", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_child_class_init_uses_super_defaults", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_child_class_kwarg_is_passed_to_super_init", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_determine_build_location_returns_a_str_path[branch1-changelist1-//filer.test/builds/DICE/userbuild/test_user/offsite_basic_drone/branch1/changelist1]", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_determine_build_location_returns_a_str_path[branch2-changelist2-//filer.test/builds/DICE/userbuild/test_user/offsite_basic_drone/branch2/changelist2]", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_determine_build_location_returns_a_str_path[branch3-changelist3-//filer.test/builds/DICE/userbuild/test_user/offsite_basic_drone/branch3/changelist3]", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_generate_shift_template_act_according_to_skuid[filer\\\\path\\\\final-shift_data0-1]", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_generate_shift_template_act_according_to_skuid[filer\\\\path\\\\final-shift_data1-0]", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_generate_shift_template_act_according_to_skuid[filer\\\\path\\\\final-shift_data2-0]", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_initialization_exception_raised_when_passed_param_is_none", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_initialization_uses_super_defaults_when_passed_param_is_set", "elipy2/tests/test_shifters.py::TestOffsiteBasicDroneShifter::test_platform_instance_variable_set_to_tools", "elipy2/tests/test_shifters.py::TestShiftParameterSetting::test_child_class_kwarg_is_passed_to_super_init", "elipy2/tests/test_shifters.py::TestShifterFactory::test_get_shifter_instance_failure", "elipy2/tests/test_shifters.py::TestShifterFactory::test_get_shifter_instance_frosty", "elipy2/tests/test_shifters.py::TestShifterFactory::test_get_shifter_instance_offiste_drone", "elipy2/tests/test_shifters.py::TestShifterFactory::test_get_shifter_instance_offsite_basic_drone", "elipy2/tests/test_steam_utils.py::TestSteamUtils::test_download_steam_sdk", "elipy2/tests/test_steam_utils.py::TestSteamUtils::test_log_in_to_steam_and_run", "elipy2/tests/test_steam_utils.py::TestSteamUtils::test_log_in_to_steam_and_run_no_quit", "elipy2/tests/test_steam_utils.py::TestSteamUtils::test_retrieve_steam_credentials", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation__run_playstation_symupload_core_exception", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_file", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_missing_source_indexing", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_no_compress", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_path", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_with_force", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_with_log", "elipy2/tests/test_symbols.py::TestSymbols::test__playstation_symbol_store_upload_with_source_indexing", "elipy2/tests/test_symbols.py::TestSymbols::test__run_playstation_symupload", "elipy2/tests/test_symbols.py::TestSymbols::test__run_playstation_symupload_not_in_path", "elipy2/tests/test_symbols.py::TestSymbols::test__verify_playstation_symbol_store_upload", "elipy2/tests/test_symbols.py::TestSymbols::test__verify_playstation_symbol_store_upload_file_missing", "elipy2/tests/test_symbols.py::TestSymbols::test__win64_symbol_pattern", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_file_with_makedirs", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_file_without_source", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_invalid_platform", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_args", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_branch", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_changelist", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_changelist_with_platform_release", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_config", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_no_platform", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_single_file", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_tools", "elipy2/tests/test_symbols.py::TestSymbols::test_backup_symbols_with_custom_includes", "elipy2/tests/test_symbols.py::TestSymbols::test_check_symbol_size_big", "elipy2/tests/test_symbols.py::TestSymbols::test_copy_symbols_to_kobold_inbox", "elipy2/tests/test_symbols.py::TestSymbols::test_copy_symbols_to_kobold_inbox_no_inbox_for_branch", "elipy2/tests/test_symbols.py::TestSymbols::test_copy_symbols_to_kobold_inbox_no_settings", "elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin", "elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin_no_dir", "elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin_no_file", "elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin_trial", "elipy2/tests/test_symbols.py::TestSymbols::test_fake_ooawrap_bin_with_path", "elipy2/tests/test_symbols.py::TestSymbols::test_find_symbol_files", "elipy2/tests/test_symbols.py::TestSymbols::test_find_symbol_files_no_dir", "elipy2/tests/test_symbols.py::TestSymbols::test_find_symstore_exe_file", "elipy2/tests/test_symbols.py::TestSymbols::test_find_tools_location", "elipy2/tests/test_symbols.py::TestSymbols::test_find_tools_location_not_found", "elipy2/tests/test_symbols.py::TestSymbols::test_init", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_fake", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_linux", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_path", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps4", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps4_binaries", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps4_binaries_no_file", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps4_binaries_no_source_index_file", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps5", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_ps5_binaries", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_tool", "elipy2/tests/test_symbols.py::TestSymbols::test_source_index_win64_binaries", "elipy2/tests/test_symbols.py::TestSymbols::test_strip_linux_symbols", "elipy2/tests/test_symbols.py::TestSymbols::test_strip_linux_symbols_add_files", "elipy2/tests/test_symbols.py::TestSymbols::test_strip_linux_symbols_non_cygwin", "elipy2/tests/test_symbols.py::TestSymbols::test_strip_linux_symbols_non_existing_build_path", "elipy2/tests/test_symbols.py::TestSymbols::test_strip_no_buildinfo_file", "elipy2/tests/test_symbols.py::TestSymbols::test_strip_no_linux64_file", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_game_binary", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_game_binary_retail", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_default_value", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_linux", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_no_store", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_nocompress", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_ps4", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_ps4_nocompress", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_retry", "elipy2/tests/test_symbols.py::TestSymbols::test_upload_symbols_to_sym_store_symfiles", "elipy2/tests/test_symbols.py::TestSymbols::test_verify_symbol_integrity", "elipy2/tests/test_symbols.py::TestSymbols::test_verify_symbol_integrity_command_failed", "elipy2/tests/test_symbols.py::TestSymbols::test_verify_symbol_integrity_found_large_pdb", "elipy2/tests/test_symbols.py::TestSymbols::test_verify_symbol_integrity_not_enough_arguments", "elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_playstation_symbol_store_upload_with_pdb_error_ignored", "elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_playstation_symbol_store_upload_with_pdb_error_not_ignored", "elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_run_upload_symbols_to_sym_store_with_pdb_error_ignored", "elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_run_upload_symbols_to_sym_store_with_pdb_error_not_ignored", "elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_verify_playstation_symbol_store_upload_with_pdb_error_ignored", "elipy2/tests/test_symbols_pdb_errors.py::TestSymbolsPdbErrors::test_verify_playstation_symbol_store_upload_with_pdb_error_not_ignored", "elipy2/tests/test_telemetry.py::TestAddBreadcrumbs::test_add_breadcrumb", "elipy2/tests/test_telemetry.py::TestCoreModule::test_get_data", "elipy2/tests/test_telemetry.py::TestCoreModule::test_get_elasticsearch_index", "elipy2/tests/test_telemetry.py::TestCoreModule::test_get_function_data", "elipy2/tests/test_telemetry.py::TestCoreModule::test_log_error_to_es", "elipy2/tests/test_telemetry.py::TestCoreModule::test_modify_args", "elipy2/tests/test_telemetry.py::TestCoreModule::test_modify_dict", "elipy2/tests/test_telemetry.py::TestCoreModule::test_upload_metrics", "elipy2/tests/test_telemetry.py::TestCoreModule::test_upload_metrics_fail", "elipy2/tests/test_telemetry.py::TestCreateDictFromArgs::test_with_args", "elipy2/tests/test_telemetry.py::TestCreateDictFromArgs::test_with_kwargs", "elipy2/tests/test_telemetry.py::TestDropTelemetryEvents::test", "elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_in_dict_value", "elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_in_list_value", "elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_in_value", "elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_kwargs", "elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_kwargs_casing", "elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_kwargs_custom_object", "elipy2/tests/test_telemetry.py::TestFilterSensitiveData::test_password_kwargs_none", "elipy2/tests/test_telemetry.py::TestGetAzurePipelineData::test_is_azure_environment_linux", "elipy2/tests/test_telemetry.py::TestGetAzurePipelineData::test_is_azure_environment_windows", "elipy2/tests/test_telemetry.py::TestGetAzurePipelineData::test_not_azure_environment", "elipy2/tests/test_telemetry.py::TestGetFrostbiteData::test_not_fbenv_environment", "elipy2/tests/test_telemetry.py::TestGetFrostbiteData::test_valid_environment_vars", "elipy2/tests/test_telemetry.py::TestGetJenkinsData::test_is_jen<PERSON>_environment", "elipy2/tests/test_telemetry.py::TestGetJenkinsData::test_not_jen<PERSON>_environment", "elipy2/tests/test_telemetry.py::TestGetMachineData::test_windows", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[/MT:12]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[D:\\\\some_path]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[a_user]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[https://some_site.ea.com]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_not_sensitive[some_s3cr3t_pa55w0rd]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[_apikey_]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[_password_]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[_token_]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[*******************************.ea.com]", "elipy2/tests/test_telemetry.py::TestIsSensitiveString::test_is_sensitive[start:end]", "elipy2/tests/test_telemetry.py::TestMetadata::test_correct_metadata", "elipy2/tests/test_telemetry.py::TestMetadata::test_retry_mocking_attributes", "elipy2/tests/test_testutils.py::TestGotButExpected::test_got_but_expected", "elipy2/tests/test_usage.py::TestSleepUsage::test_never_sleep_more_than_30_seconds", "elipy2/tests/test_vault.py::TestVault::test_save_symbols_locally", "elipy2/tests/test_vault.py::TestVault::test_save_symbols_locally_clean", "elipy2/tests/test_vault.py::TestVault::test_save_vaulting_logs", "elipy2/tests/test_vault.py::TestVault::test_validate_build_files", "elipy2/tests/test_vault.py::TestVault::test_validate_build_files_no_files", "elipy2/tests/test_vault.py::TestVault::test_validate_build_files_notset", "elipy2/tests/test_vault.py::TestVault::test_vault", "elipy2/tests/test_vault.py::TestVault::test_vault_anything_with_checksum", "elipy2/tests/test_vault.py::TestVault::test_vault_anything_with_checksum_no_dest", "elipy2/tests/test_vault.py::TestVault::test_vault_build", "elipy2/tests/test_vault.py::TestVault::test_vault_build_dry_run", "elipy2/tests/test_vault.py::TestVault::test_vault_build_no_log", "elipy2/tests/test_vault.py::TestVault::test_vault_symbols_dry_run", "elipy2/tests/test_vault.py::TestVault::test_vault_symbols_nolog", "elipy2/tests/test_vault.py::TestVault::test_vault_symbols_with_symstore_error_ignored", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_buildmachine_check", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_buildmachine_check_false", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_carbonblack", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_convert_bytes_to_human_readable", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_create_reg", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_delete_service_pass", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_get_computer_name", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_get_free_disk_space", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_get_reg", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_query_service_not_installed", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_query_service_running", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_query_service_stopped", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_query_service_unknown", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_service_installed_false", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_service_installed_true", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_set_reg", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_shutdown_service", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_shutdown_service_error", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_shutdown_service_running", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_start_service", "elipy2/tests/test_windows_tools.py::TestCoreModule::test_time"]