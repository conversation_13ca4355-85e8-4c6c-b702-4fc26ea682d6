#!/usr/bin/env python3
"""
Simple test script to verify the sanitizers branch discovery logic
"""
import os

def _scan_code_branches(path):
    """Scan code paths for branches: code/branch/CL

    Special handling for sanitizers paths where structure is:
    code/ch1-content-dev-sanitizers/asan/CL
    code/ch1-content-dev-sanitizers/ubsan/CL
    """
    branches = set()

    for item in os.listdir(path):
        item_path = os.path.join(path, item)
        if os.path.isdir(item_path):
            # Check if this is a sanitizers directory
            if "sanitizers" in item.lower():
                # For sanitizers, look for subdirectories (asan, ubsan) and treat them as separate branches
                try:
                    subitems = os.listdir(item_path)
                    for subitem in subitems:
                        subitem_path = os.path.join(item_path, subitem)
                        if os.path.isdir(subitem_path):
                            # Check if this subdirectory contains CL directories
                            try:
                                subsubitems = os.listdir(subitem_path)
                                has_cl_dirs = any(
                                    subsub.isdigit() and len(subsub) >= 7
                                    for subsub in subsubitems
                                    if os.path.isdir(os.path.join(subitem_path, subsub))
                                )
                                if has_cl_dirs:
                                    # Add the full path as branch name for sanitizers
                                    # This will create branch names like "ch1-content-dev-sanitizers/asan"
                                    branches.add(f"{item}/{subitem}")
                            except (OSError, PermissionError):
                                # If we can't read the directory, still consider it a potential branch
                                branches.add(f"{item}/{subitem}")
                except (OSError, PermissionError):
                    # If we can't read the sanitizers directory, treat it as a regular branch
                    branches.add(item)
            else:
                # Standard branch handling
                branches.add(item)

    return branches

def test_sanitizers_branch_discovery():
    """Test the updated _scan_code_branches function with sanitizers path"""
    
    # Test path for sanitizers
    sanitizers_path = r"\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers"
    
    print(f"Testing sanitizers branch discovery for: {sanitizers_path}")
    print("=" * 80)
    
    try:
        branches = _scan_code_branches(sanitizers_path)
        print(f"Found {len(branches)} branches:")
        for branch in sorted(branches):
            print(f"  - {branch}")
            
        # Verify we found the expected branches
        expected_branches = {"ch1-content-dev-sanitizers/asan", "ch1-content-dev-sanitizers/ubsan"}
        found_branches = set(branches)
        
        print("\nExpected branches:", expected_branches)
        print("Found branches:", found_branches)
        
        if expected_branches.issubset(found_branches):
            print("✅ SUCCESS: All expected sanitizers branches found!")
            return True
        else:
            missing = expected_branches - found_branches
            print(f"❌ MISSING: {missing}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_directory_structure():
    """Verify the actual directory structure"""
    sanitizers_path = r"\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers"
    
    print(f"\nVerifying directory structure at: {sanitizers_path}")
    print("=" * 80)
    
    try:
        items = os.listdir(sanitizers_path)
        print(f"Found {len(items)} items:")
        for item in sorted(items):
            item_path = os.path.join(sanitizers_path, item)
            if os.path.isdir(item_path):
                try:
                    subitems = os.listdir(item_path)
                    cl_count = sum(1 for subitem in subitems 
                                 if subitem.isdigit() and len(subitem) >= 7 
                                 and os.path.isdir(os.path.join(item_path, subitem)))
                    print(f"  📁 {item}/ ({cl_count} CL directories)")
                except (OSError, PermissionError):
                    print(f"  📁 {item}/ (access denied)")
            else:
                print(f"  📄 {item}")
                
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    print("Testing sanitizers branch discovery fix")
    print("=" * 80)
    
    verify_directory_structure()
    success = test_sanitizers_branch_discovery()
    
    print(f"\nTest {'PASSED' if success else 'FAILED'}!")
