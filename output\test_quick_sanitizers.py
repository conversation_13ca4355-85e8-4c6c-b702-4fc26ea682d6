#!/usr/bin/env python3
"""
Quick test to verify the sanitizers fix works
"""
import os

def test_asan_ubsan_exist():
    """Quick test to verify asan and ubsan directories exist"""
    sanitizers_path = r"\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers"
    
    print("Quick verification of asan/ubsan directories:")
    print("=" * 50)
    
    asan_path = os.path.join(sanitizers_path, "asan")
    ubsan_path = os.path.join(sanitizers_path, "ubsan")
    
    asan_exists = os.path.exists(asan_path)
    ubsan_exists = os.path.exists(ubsan_path)
    
    print(f"asan directory exists: {asan_exists}")
    print(f"ubsan directory exists: {ubsan_exists}")
    
    if asan_exists:
        try:
            asan_items = os.listdir(asan_path)
            asan_cl_count = sum(1 for item in asan_items 
                              if item.isdigit() and len(item) >= 7 
                              and os.path.isdir(os.path.join(asan_path, item)))
            print(f"asan CL directories: {asan_cl_count}")
        except Exception as e:
            print(f"Error reading asan: {e}")
    
    if ubsan_exists:
        try:
            ubsan_items = os.listdir(ubsan_path)
            ubsan_cl_count = sum(1 for item in ubsan_items 
                               if item.isdigit() and len(item) >= 7 
                               and os.path.isdir(os.path.join(ubsan_path, item)))
            print(f"ubsan CL directories: {ubsan_cl_count}")
        except Exception as e:
            print(f"Error reading ubsan: {e}")
    
    return asan_exists and ubsan_exists

if __name__ == "__main__":
    success = test_asan_ubsan_exist()
    print(f"\nTest {'PASSED' if success else 'FAILED'}!")
